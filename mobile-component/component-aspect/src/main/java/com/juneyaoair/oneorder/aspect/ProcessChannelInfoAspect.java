package com.juneyaoair.oneorder.aspect;

import com.juneyaoair.oneorder.api.common.CommonService;
import com.juneyaoair.oneorder.common.dto.RequestDataDto;
import com.juneyaoair.oneorder.mobile.dto.ChannelInfo;
import com.juneyaoair.oneorder.order.dto.b2corder.B2cOrderBriefReqDTO;
import com.juneyaoair.oneorder.order.dto.score.QueryMemberScoreReqDTO;
import com.juneyaoair.oneorder.restresult.response.RequestData;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Aspect
@Component
@Slf4j
public class ProcessChannelInfoAspect {


    @Autowired
    private CommonService commonService;


    /**
     * 拦截带有ProcessChannelInfo注解的方法
     */
    @Around("@annotation(com.juneyaoair.oneorder.annotation.ProcessChannelInfo) || @within(com.juneyaoair.oneorder.annotation.ProcessChannelInfo)")
    public Object interceptControllerMethods(ProceedingJoinPoint joinPoint) throws Throwable {
        processRequestData(joinPoint);
        return joinPoint.proceed();
    }


    /**
     * 处理请求数据中的渠道信息
     */
    private void processRequestData(ProceedingJoinPoint joinPoint) {
        Object[] args = joinPoint.getArgs();

        for (Object arg : args) {
            if (arg instanceof RequestData) {
                RequestData<?> requestData = (RequestData<?>) arg;

                try {
                    ChannelInfo channelInfo = commonService.findChannelInfo(requestData.getChannelNo());
                    if (channelInfo != null) {
                        requestData.setChannelNo(channelInfo.getOrderChannelCode());
                    }
                } catch (Exception e) {
                    log.warn("处理渠道信息时发生异常: ", e);
                }
            } else if (arg instanceof RequestDataDto) {
                RequestDataDto<?> requestData = (RequestDataDto<?>) arg;

                try {
                    ChannelInfo channelInfo = commonService.findChannelInfo(requestData.getChannelNo());
                    if (channelInfo != null) {
                        requestData.setChannelNo(channelInfo.getOrderChannelCode());
                    }
                } catch (Exception e) {
                    log.warn("处理渠道信息时发生异常: ", e);
                }
            }
        }
    }


}
