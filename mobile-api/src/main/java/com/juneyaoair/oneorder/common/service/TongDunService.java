package com.juneyaoair.oneorder.common.service;

import com.juneyaoair.flightbasic.antifraud.FinalDecisionEnum;
import com.juneyaoair.flightbasic.antifraud.request.Antifraud;
import com.juneyaoair.flightbasic.antifraud.response.AntiResult;
import com.juneyaoair.flightbasic.appenum.MemberDetailRequestItemsEnum;
import com.juneyaoair.flightbasic.common.BaseRequestDTO;
import com.juneyaoair.flightbasic.common.BaseResponseDTO;
import com.juneyaoair.flightbasic.common.WSEnum;
import com.juneyaoair.flightbasic.feign.FlightBasicConsumerClient;
import com.juneyaoair.flightbasic.response.api.ApiCityInfoDto;
import com.juneyaoair.mobile.exception.MultiLangServiceException;
import com.juneyaoair.mobile.exception.ServiceException;
import com.juneyaoair.mobile.exception.errorcode.CommonErrorCode;
import com.juneyaoair.mobile.exception.util.HoAirIpUtil;
import com.juneyaoair.oneorder.api.common.CacheService;
import com.juneyaoair.oneorder.api.common.CommonService;
import com.juneyaoair.oneorder.api.crm.service.IMemberService;
import com.juneyaoair.oneorder.api.crm.utils.CRMReqUtil;
import com.juneyaoair.oneorder.common.common.AccountTypeEnum;
import com.juneyaoair.oneorder.common.common.LoginTypeEnum;
import com.juneyaoair.oneorder.common.constant.ContactTypeEnum;
import com.juneyaoair.oneorder.common.constant.TongDunEventType;
import com.juneyaoair.oneorder.common.dto.BizDto;
import com.juneyaoair.oneorder.common.dto.RequestDataDto;
import com.juneyaoair.oneorder.common.dto.enums.CaptchaFuncEnum;
import com.juneyaoair.oneorder.common.dto.enums.ChannelCodeEnum;
import com.juneyaoair.oneorder.common.dto.enums.SensitiveOperationEnum;
import com.juneyaoair.oneorder.common.util.IPUtil;
import com.juneyaoair.oneorder.common.util.TongDunUtil;
import com.juneyaoair.oneorder.config.TongDunConfig;
import com.juneyaoair.oneorder.crm.dto.PtApiCRMRequest;
import com.juneyaoair.oneorder.crm.dto.PtCRMResponse;
import com.juneyaoair.oneorder.crm.dto.common.MemberBasicInfoSoaModel;
import com.juneyaoair.oneorder.crm.dto.common.MemberContactSoaModel;
import com.juneyaoair.oneorder.crm.dto.request.PtMemberDetailRequest;
import com.juneyaoair.oneorder.crm.dto.response.PtMemberDetail;
import com.juneyaoair.oneorder.fare.dto.display.FlightInfoCombDTO;
import com.juneyaoair.oneorder.fare.dto.display.FlightInfoDTO;
import com.juneyaoair.oneorder.fare.dto.display.QueryFlightInfoReqDTO;
import com.juneyaoair.oneorder.mobile.config.AppConfig;
import com.juneyaoair.oneorder.mobile.dto.ChannelInfo;
import com.juneyaoair.oneorder.order.dto.display.TicketBookMultiReqDTO;
import com.juneyaoair.oneorder.order.util.AirStringUtil;
import com.juneyaoair.oneorder.restresult.response.RequestData;
import com.juneyaoair.oneorder.tools.utils.DateUtil;
import com.juneyaoair.oneorder.tools.utils.HoAirGsonUtil;
import com.juneyaoair.oneorder.tools.utils.dto.CrmPhoneInfo;
import com.juneyaoair.oneorder.util.CIDRUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/9/22 15:59
 */
@Service
@Slf4j
public class TongDunService extends CommonService {
    @Resource
    private TongDunConfig tongDunConfig;
    @Resource
    private FlightBasicConsumerClient flightBasicConsumerClient;
    @Resource
    private HttpServletRequest httpServletRequest;
    @Resource
    private IMemberService memberService;
    @Resource
    private CacheService cacheService;
    @Value("${oneorder.tongDunPermitIpSwitch:Y}")
    private String tongDunPermitIpSwitch;

    @Value("${oneorder.tongDunPermitIpCidr:**********/16}")
    private String tongDunPermitIpCidr;

    @Value("${oneorder.tongDunPermitChannelCode:HOCAR}")
    private String tongDunPermitChannelCode;

    @Resource
    private AppConfig appConfig;

    // 国内运价同盾开关
    @Value("${oneorder.dom.tongDunSwitch:N}")
    private String domTongDunSwitch;

    // 国际运价同盾开关
    @Value("${oneorder.inter.tongDunSwitch:N}")
    private String interTongDunSwitch;
    /**
     * 航班查询同盾处理
     *
     * @param \
     * @return
     */
    public void fareAntiFraud(RequestData<QueryFlightInfoReqDTO> requestData, BizDto bizDto) throws ServiceException {
        log.info("航班查询同盾处理01-fareAntiFraud-requestData={}-bizDto={}", HoAirGsonUtil.objectToJson(requestData), HoAirGsonUtil.objectToJson(bizDto));
        String ip = HoAirIpUtil.getIpAddr(httpServletRequest);
        //如果是内网IP，直接放行
        if (BooleanUtils.toBoolean(tongDunPermitIpSwitch) && ifIpPermit(ip)) {
            log.info("当前请求同盾放行，请求IP为：{}，当前放行网段为{}",ip,tongDunPermitIpCidr);
            return;
        }
        String channelNo = requestData.getChannelNo();
        //如果是放行渠道，直接放行
        if (ifChannelCodePermit(channelNo)) {
            return;
        }
        String ffpId = requestData.getFfpId();
        String ffpNo = requestData.getFfpNo();
        QueryFlightInfoReqDTO reqDTO = requestData.getData();
        String blackBox = reqDTO.getBlackBox();
        String userAgent = httpServletRequest.getHeader("userAgent");
        if (AirStringUtil.isEmpty(blackBox)) {
            MultiLangServiceException.fail(CommonErrorCode.QUICK_VERIFY_FAIL);
        } else {
            log.info("航班查询同盾处理02-fareAntiFraud-channelNo={}-bizDto={}", channelNo, HoAirGsonUtil.objectToJson(bizDto));
            String eventType = TongDunEventType.LOOKUP.getEventType();
            if (ChannelCodeEnum.G_B2C.name().equalsIgnoreCase(bizDto.getHeadChannelCode())) {
                eventType = TongDunEventType.BROWSE.getEventType();
            }
            Map<String, String> commonParam = TongDunUtil.createCommonParam(bizDto.getHeadChannelCode(), bizDto.getPlatformInfo(), bizDto.getFrom(), eventType, SensitiveOperationEnum.QUERY_FLIGHT_RISK_CTRL.name(), tongDunConfig);
            if (ObjectUtils.isEmpty(commonParam)) {//说明没有对应的事件ID
                MultiLangServiceException.fail(CommonErrorCode.QUICK_VERIFY_FAIL);
            }
            // 设备指纹
            commonParam.put("black_box", blackBox);
            commonParam.put("user_agent", userAgent);
            commonParam.put("ip_address", ip);
            String depCity = reqDTO.getSegCondList().get(0).getDepCity();
            String arrCity = reqDTO.getSegCondList().get(0).getArrCity();
            // 判断是否使用同盾校验
//            if (!judgeIsOpenTongDun(depCity, arrCity)) {
//                return;
//            }
            if (AirStringUtil.isNotEmpty(depCity)) {
                commonParam.put("ext_takeoff_place", cacheService.getLocalCity(depCity).getCityName());
            }
            if (AirStringUtil.isNotEmpty(arrCity)) {
                commonParam.put("ext_arrive_place", cacheService.getLocalCity(arrCity).getCityName());
            }
            commonParam.put("ext_departure_time", DateUtil.formatDate(DateUtil.parseDate(reqDTO.getSegCondList().get(0).getFlightDate()), DateUtil.YYYY_MM_DD_HH_MM_SS_PATTERN));
            if (!AirStringUtil.isEmpty(ffpNo)) {
                commonParam.put("account_login", ffpNo);
            }
            BaseRequestDTO<Antifraud> antifraudBaseRequestDTO = new BaseRequestDTO<>();
            antifraudBaseRequestDTO.setIp(ip);
            antifraudBaseRequestDTO.setChannelCode(channelNo);
            antifraudBaseRequestDTO.setFfpCardNo(ffpNo);
            antifraudBaseRequestDTO.setFfpId(ffpId);
            antifraudBaseRequestDTO.setUserNo("10001");
            antifraudBaseRequestDTO.setVersion("10");
            Antifraud antifraud = new Antifraud();
            antifraud.setIp(ip);
            antifraud.setFfpCardNo(ffpNo);
            antifraud.setFfpId(ffpId);
            antifraud.setParamMap(commonParam);
            antifraudBaseRequestDTO.setRequest(antifraud);
            log.info("运价同盾校验，入参：{}", HoAirGsonUtil.objectToJson(antifraudBaseRequestDTO));
            BaseResponseDTO<AntiResult> antiFraudResult = flightBasicConsumerClient.antifraud(antifraudBaseRequestDTO);
            log.info("运价同盾校验，出参：{}", HoAirGsonUtil.objectToJson(antiFraudResult));
            if (antiFraudResult == null || !"10001".equals(antiFraudResult.getResultCode())) {
                MultiLangServiceException.fail(CommonErrorCode.QUICK_VERIFY_FAIL);
            }
            if (null != antiFraudResult.getObjData() && FinalDecisionEnum.REJECT.getCode().equalsIgnoreCase(antiFraudResult.getObjData().getFinal_decision())) {
                log.info("运价同盾拒绝，同盾返回Reject");
                ServiceException.fail("您的账户存在风险，无法继续操作，如有疑问请咨询官方客服");
            }
            if (null != antiFraudResult.getObjData() && antiFraudResult.getObjData().getFinal_score() != null && antiFraudResult.getObjData().getFinal_score() > tongDunConfig.getTongDunFinalScoreLimit()) {
                log.info("运价同盾拒绝，最终分数高于警戒值，用户分数为：{}，当前警戒值为：{}", antiFraudResult.getObjData().getFinal_score(), tongDunConfig.getTongDunFinalScoreLimit());
                MultiLangServiceException.fail(CommonErrorCode.ACCOUNT_WARNING);
            }
        }

    }

    /***
     * 判断是否使用同盾校验
     * @param depCity 出发城市
     * @param arrCity 到达城市
     * @return
     */
    private boolean judgeIsOpenTongDun(String depCity, String arrCity) {
        boolean b = judegIsInternational(depCity, arrCity);
        log.info("judgeIsOpenTongDun-出发城市或到达城市是否是国际航线={}", b);
        return ("Y".equalsIgnoreCase(domTongDunSwitch) && !b) || ("Y".equalsIgnoreCase(interTongDunSwitch) && b);
    }

    /***
     * 判断是否是国际航线
     * @param depCity 出发城市
     * @param arrCity 到达城市
     * @return
     */
    private boolean judegIsInternational(String depCity, String arrCity) {
        ApiCityInfoDto depCityDto = new ApiCityInfoDto();
        if (AirStringUtil.isNotBlank(depCity)) {
            depCityDto = cacheService.getLocalCity(depCity);
        }
        ApiCityInfoDto arrCityDto = new ApiCityInfoDto();
        if (AirStringUtil.isNotBlank(depCity)) {
            arrCityDto = cacheService.getLocalCity(arrCity);
        }
        return "I".equals(depCityDto.getIsInternational()) || "I".equals(arrCityDto.getIsInternational());
    }


    /**
     * 预定查询同盾处理
     *
     * @param \
     * @return
     */
    public void bookAntiFraud(RequestData<TicketBookMultiReqDTO> requestData, BizDto bizDto) throws ServiceException {
        log.info("机票预定同盾处理01-bookAntiFraud-bizDto={}", HoAirGsonUtil.objectToJson(bizDto));
        //如果是内网IP，直接放行
        String ip = HoAirIpUtil.getIpAddr(httpServletRequest);
        if (BooleanUtils.toBoolean(tongDunPermitIpSwitch) && ifIpPermit(ip)) {
            log.info("当前请求同盾放行，请求IP为：{}，当前放行网段为{}",ip,tongDunPermitIpCidr);
            return;
        }
        String channelNo = requestData.getChannelNo();
        String ffpId = requestData.getFfpId();
        String ffpNo = requestData.getFfpNo();
        TicketBookMultiReqDTO reqDTO = requestData.getData();
        String blackBox = reqDTO.getBlackBox();
        log.info("机票预定同盾安全校验-设备指纹blackBox={}", blackBox);
        if (AirStringUtil.isEmpty(blackBox)) {
            MultiLangServiceException.fail(CommonErrorCode.QUICK_VERIFY_FAIL);
        }
        String buyMobile = "";
        String buyEmail = "";
        String buyName = "";
        // 游客直接取参数值，不用查询会员信息
        if (AirStringUtil.isBlank(ffpId) || AirStringUtil.isBlank(ffpNo)) {
            buyMobile = reqDTO.getLinkerHandphone();
            buyEmail = reqDTO.getLinkerEmail();
            buyName = reqDTO.getLinker();
            log.info("机票预订游客无需查询会员信息-buyMobile={}-buyEmail={}-buyName={}", buyMobile, buyEmail, buyName);
        } else {
            ChannelInfo channelInfo = findChannelInfo(bizDto.getHeadChannelCode());
            String[] items = {MemberDetailRequestItemsEnum.STATEINFO.eName, MemberDetailRequestItemsEnum.BASICINFO.eName, MemberDetailRequestItemsEnum.CONTACTINFO.eName, MemberDetailRequestItemsEnum.CERTIFICATEINFO.eName, MemberDetailRequestItemsEnum.REALVERIFYINFOS.eName};
            PtApiCRMRequest<PtMemberDetailRequest> ptApiRequest = CRMReqUtil.buildMemberDetailReq(ffpNo, ffpId, ip, channelInfo.getChannelCode(), channelInfo.getChannelPwd(), items);
            PtCRMResponse<PtMemberDetail> ptCRMResponse = memberService.memberDetail(ptApiRequest, false);
            MemberBasicInfoSoaModel basicInfo = ptCRMResponse.getData().getBasicInfo();
            buyName = CRMReqUtil.getMemberName(basicInfo);
            List<MemberContactSoaModel> contactSoaModelList = ptCRMResponse.getData().getContactInfo();
            //联系方式
            if (CollectionUtils.isNotEmpty(contactSoaModelList)) {
                for (MemberContactSoaModel con : contactSoaModelList) {
                    //手机号
                    if (con.getContactType() == ContactTypeEnum.MOBILE.getCode()) {
                        buyMobile = con.getContactNumber();
                    }
                    //邮箱
                    if (con.getContactType() == ContactTypeEnum.EMAIL.getCode()) {
                        buyEmail = con.getContactNumber();
                    }
                }
            }
        }
        log.info("机票预定同盾处理02-bookAntiFraud-channelNo={}-bizDto={}", channelNo, HoAirGsonUtil.objectToJson(bizDto));
        Map<String, String> commonParam = TongDunUtil.createCommonParam(bizDto.getHeadChannelCode(), bizDto.getPlatformInfo(), bizDto.getFrom(), TongDunEventType.TRADE.getEventType(), SensitiveOperationEnum.TRADE_RISK_CTRL.name(), tongDunConfig);
        if (ObjectUtils.isEmpty(commonParam)) {
            MultiLangServiceException.fail(CommonErrorCode.QUICK_VERIFY_FAIL);
        }
        //买家IP
        commonParam.put("ip_address", ip);
        //买家姓名
        commonParam.put("account_name", buyName);
        //买家手机
        commonParam.put("account_mobile", buyMobile == null ? "" : buyMobile);
        //买家邮箱
        commonParam.put("account_email", buyEmail == null ? "" : buyEmail);
        // 设备指纹
        commonParam.put("black_box", blackBox);
        commonParam.put("account_login", ffpNo);
        //乘客姓名
        commonParam.put("passenger_name", reqDTO.getPassengerInfoList().get(0).getPassengerName());
        //联系人手机号
        commonParam.put("ext_passenger_mobile", reqDTO.getLinkerHandphone());
        //渠道订单编号
        commonParam.put("transaction_id", reqDTO.getChannelOrderNo());
//        commonParam.put("ext_version_number", reqDTO.getClientVersion());
        //2019-06-19新增
//        com.juneyaoair.baseclass.response.av.FlightInfo flightInfo = reqDTO.getFlightInfoComb().getCombFlightInfoList().get(0);
        // 国内国际标识，D：国内，I：国际
        commonParam.put("ext_is_international", reqDTO.getInterFlag());
        List<FlightInfoCombDTO> flightInfoCombList = reqDTO.getFlightInfoCombList();
        if (CollectionUtils.isNotEmpty(flightInfoCombList)) {
            FlightInfoCombDTO flightInfoCombDTO = flightInfoCombList.get(0);
            commonParam.put("ext_departure_time", DateUtil.formatDate(DateUtil.parseDate(flightInfoCombDTO.getDepDateTime()), DateUtil.YYYY_MM_DD_HH_MM_SS_PATTERN));
            List<FlightInfoDTO> flightInfoList = flightInfoCombDTO.getFlightInfoList();
            if (CollectionUtils.isNotEmpty(flightInfoList)) {
                FlightInfoDTO flightInfoDTO = flightInfoList.get(0);
                commonParam.put("ext_takeoff_place", flightInfoDTO.getDepCityName());
                commonParam.put("ext_arrive_place", flightInfoDTO.getArrCityName());
                commonParam.put("ext_flight_number", flightInfoDTO.getFlightNo());
                commonParam.put("ext_is_transit", Integer.parseInt(flightInfoDTO.getStopNumber()) > 0 ? "1" : "0");
            }
        }
        commonParam.put("ext_is_roundtrip", "RT".equals(reqDTO.getRouteType()) ? "1" : "0");
        commonParam.put("ext_passenger_count", "" + reqDTO.getPassengerInfoList().size());
        BaseRequestDTO<Antifraud> antifraudBaseRequestDTO = new BaseRequestDTO<>();
        antifraudBaseRequestDTO.setIp(ip);
        antifraudBaseRequestDTO.setChannelCode(channelNo);
        antifraudBaseRequestDTO.setFfpCardNo(ffpNo);
        antifraudBaseRequestDTO.setFfpId(ffpId);
        antifraudBaseRequestDTO.setUserNo("10001");
        antifraudBaseRequestDTO.setVersion("10");
        Antifraud antifraud = new Antifraud();
        antifraud.setIp(ip);
        antifraud.setFfpCardNo(ffpNo);
        antifraud.setFfpId(ffpId);
        antifraud.setParamMap(commonParam);
        antifraudBaseRequestDTO.setRequest(antifraud);
        log.info("下单同盾校验，渠道订单号：{},入参：{}", reqDTO.getChannelOrderNo(), HoAirGsonUtil.objectToJson(antifraudBaseRequestDTO));
        BaseResponseDTO<AntiResult> antiFraudResult = flightBasicConsumerClient.antifraud(antifraudBaseRequestDTO);
        log.info("下单同盾校验，渠道订单号：{},出参：{}", reqDTO.getChannelOrderNo(), HoAirGsonUtil.objectToJson(antiFraudResult));
        if (antiFraudResult == null || !"10001".equals(antiFraudResult.getResultCode())) {
            MultiLangServiceException.fail(CommonErrorCode.QUICK_VERIFY_FAIL);
        }
        if (null != antiFraudResult.getObjData() && FinalDecisionEnum.REJECT.getCode().equalsIgnoreCase(antiFraudResult.getObjData().getFinal_decision())) {
            log.info("下单同盾拒绝，同盾返回Reject");
            MultiLangServiceException.fail(CommonErrorCode.ACCOUNT_WARNING);
        }

    }

    /**
     * @param bizDto
     * @param blackBox
     * @param account
     * @param accountType   账户类型
     * @param loginTypeEnum 登录方式
     * @return void
     * @description 登录风控同盾处理
     * <AUTHOR>
     * @date 2024/12/31 15:17
     **/
    public void loginAntiFraud(BizDto bizDto, String blackBox, String account, AccountTypeEnum accountType, LoginTypeEnum loginTypeEnum) {
        Map<String, String> paramMap = TongDunUtil.createCommonParam(bizDto.getHeadChannelCode(), bizDto.getPlatformInfo(), bizDto.getFrom(), TongDunEventType.LOGIN.getEventType(), SensitiveOperationEnum.LOGIN_RISK_CTRL.name(), tongDunConfig);
        paramMap.put("black_box", blackBox);
        paramMap.put("ip_address", bizDto.getIp());
        paramMap.put("login_type", loginTypeEnum.getName());
        // 其他可选参数 - 如帐户名
        if (StringUtils.hasText(account)) {
            if (AccountTypeEnum.EMAIL == accountType) {
                //登录邮箱
                paramMap.put("account_email", account);
            } else if (AccountTypeEnum.PHONE == accountType) {
                //登录手机号
                paramMap.put("account_mobile", account);
            } else {
                paramMap.put("account_login", account);
            }
        }
        Antifraud antifraud = new Antifraud();
        antifraud.setParamMap(paramMap);
        antifraud.setIp(bizDto.getIp());
        antifraud(bizDto.getIp(), bizDto.getHeadChannelCode(), antifraud);
    }

    /**
     * 客票验真
     *
     * @param bizDto
     * @param blackBox
     * @param ffpCardNo
     */
    public void ticketVerify(BizDto bizDto, String blackBox, String ffpId, String ffpCardNo, String mobile, String ticketNumber) {
        TongDunEventType tongDunEventType = TongDunEventType.LOOKUP;
        if (ChannelCodeEnum.B2C.getChannelCode().equals(bizDto.getHeadChannelCode())) {
            tongDunEventType = TongDunEventType.VERIFICATION;
        }
        Map<String, String> commonParam = TongDunUtil.createCommonParam(bizDto.getHeadChannelCode(), bizDto.getPlatformInfo(), bizDto.getFrom(), tongDunEventType.getEventType(), SensitiveOperationEnum.TICKET_RISK_CTRL.name(), tongDunConfig);
        //同盾业务参数
        // 设备指纹
        commonParam.put("black_box", blackBox);
        commonParam.put("ip_address", bizDto.getIp());
        commonParam.put("account_login", TongDunUtil.getFfpCardNo(ffpCardNo));
        if (StringUtils.hasText(mobile)) {
            commonParam.put("account_mobile", mobile);
        }
        if (StringUtils.hasText(ticketNumber)) {
            commonParam.put("ext_ticket_number", ticketNumber);
        }
        Antifraud antifraud = new Antifraud();
        antifraud.setIp(bizDto.getIp());
        antifraud.setFfpId(ffpId);
        antifraud.setFfpCardNo(ffpCardNo);
        antifraud.setParamMap(commonParam);
        antifraud(bizDto.getIp(), bizDto.getHeadChannelCode(), antifraud);
    }


    /**
     * 短信 or 邮箱
     *
     * @param bizDto
     * @param blackBox
     * @param crmPhoneInfo
     * @param email
     * @param extScene
     * @param sign
     */
    public void sendSmsOrEmail(BizDto bizDto, String blackBox, CrmPhoneInfo crmPhoneInfo, String email, CaptchaFuncEnum extScene, String sign) {
        Map<String, String> commonParam = TongDunUtil.createCommonParam(bizDto.getHeadChannelCode(), bizDto.getPlatformInfo(), bizDto.getFrom(), TongDunEventType.SMS.getEventType(), sign, tongDunConfig);
        //同盾业务参数
        commonParam.put("black_box", blackBox);
        commonParam.put("ip_address", bizDto.getIp());
        //联系方式处理
        if (crmPhoneInfo != null) {
            //用于区分国际手机号
            commonParam.put("phoneCode", crmPhoneInfo.getAreaId());
            //账户手机号
            commonParam.put("account_mobile", crmPhoneInfo.getPhone());
        }
        //邮箱
        commonParam.put("account_email", email);
        //如果有多个短信场景，可以传入字符进行区分
        commonParam.put("ext_sms_scene", extScene.name());
        Antifraud antifraud = new Antifraud();
        antifraud.setIp(bizDto.getIp());
        antifraud.setParamMap(commonParam);
        antifraud(bizDto.getIp(), bizDto.getHeadChannelCode(), antifraud);
    }

    /**
     * 以172开头的ip不走同盾，true表示放行，false表示要走同盾
     *
     * @return
     */
    private boolean ifIpPermit(String clientIp) {
        try {
            CIDRUtils cidrUtils = new CIDRUtils(tongDunPermitIpCidr);
            return cidrUtils.contains(clientIp);
        } catch (Exception e) {
            StringBuilder exMsg = new StringBuilder();
            if (e.getStackTrace() != null && e.getStackTrace().length > 0 && e.getStackTrace()[0] != null) {
                exMsg.append(e.getStackTrace()[0].getClassName()).append(":").append(e.getStackTrace()[0].getLineNumber());
            }
            log.error("同盾IP放行方法错误，报错信息：{}，错误定位：{}", e.getMessage(), exMsg.toString());
        }
        return false;
    }

    /**
     * 指定渠道号放行同盾，true表示放行，false表示要走同盾
     *
     * @return
     */
    private boolean ifChannelCodePermit(String channelNo) {
        if (tongDunPermitChannelCode.contains(channelNo)) {
            return true;
        }
        return false;
    }

    /**
     * 值机选座行程查询相关同盾校验
     *
     * @param requestData
     * @param bizDto
     */
    public void checkInSeatControl(RequestDataDto requestData, BizDto bizDto) {
        // 车机渠道不进行同盾校验
        if (ChannelCodeEnum.HOCAR.getChannelCode().equals(bizDto.getHeadChannelCode())) {
            return;
        }
        Map<String, String> params = TongDunUtil.createCommonParam(bizDto.getHeadChannelCode(), bizDto.getPlatformInfo(), bizDto.getFrom(), TongDunEventType.LOOKUP.getEventType(), SensitiveOperationEnum.CHECK_IN_TOUR.name(), tongDunConfig);
        // 设备指纹
        params.put("black_box", requestData.getBlackBox());
        params.put("account_login", requestData.getFfpNo());
        params.put("ip_address", bizDto.getIp());
        params.put("ext_version_number", bizDto.getHeadClientVersion());
        Antifraud antifraud = new Antifraud();
        antifraud.setIp(bizDto.getIp());
        antifraud.setFfpCardNo(requestData.getFfpNo());
        antifraud.setFfpId(requestData.getFfpId());
        antifraud.setParamMap(params);
        antifraud(bizDto.getIp(), bizDto.getHeadChannelCode(), antifraud);
    }

    /**
     * 同盾验证
     *
     * @param clientIp    客户端IP
     * @param channelCode
     * @param antifraud
     * @return
     */
    private void antifraud(String clientIp, String channelCode, Antifraud antifraud) {
        BaseRequestDTO<Antifraud> baseRequest = createBaseRequestDTO(clientIp, channelCode, antifraud);
        log.info("同盾校验开始，入参：{}", HoAirGsonUtil.objectToJson(baseRequest));
        BaseResponseDTO<AntiResult> baseResponse = flightBasicConsumerClient.antifraud(baseRequest);
        log.info("同盾校验结束，出参：{}", HoAirGsonUtil.objectToJson(baseResponse));
        if (baseResponse == null || !WSEnum.SUCCESS.resultCode.equals(baseResponse.getResultCode())) {
            throw MultiLangServiceException.fail(CommonErrorCode.QUICK_VERIFY_FAIL);
        }
        AntiResult antiResult = baseResponse.getObjData();
        if (null != antiResult && FinalDecisionEnum.REJECT.getCode().equalsIgnoreCase(antiResult.getFinal_decision())) {
            log.info("{}同盾校验拒绝，同盾返回Reject", baseRequest.getServieCode());
            throw MultiLangServiceException.fail(CommonErrorCode.ACCOUNT_WARNING);
        }
    }

}
