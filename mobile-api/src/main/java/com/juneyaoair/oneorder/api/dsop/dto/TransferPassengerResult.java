package com.juneyaoair.oneorder.api.dsop.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 中转旅客信息
 * <AUTHOR>
 */
@Data
public class TransferPassengerResult {

    @ApiModelProperty(value = "票号")
    private String ticketnumber;

    @ApiModelProperty(value = "英文姓")
    private String lastname;

    @ApiModelProperty(value = "英文名")
    private String firstname;

    @ApiModelProperty(value = "护照号")
    private String passportNumber;

    @ApiModelProperty(value = "出票日期 格式：2024-02-27")
    private String issuedate;

    @ApiModelProperty(value = "中转航线")
    private String transferFlightLine;

    @ApiModelProperty(value = "中转航班号组")
    private String transferFlightNo;

    @ApiModelProperty(value = "全航线")
    private String fullFlightLine;

    @ApiModelProperty(value = "全航班号组")
    private String fullFlightNo;

    @ApiModelProperty(value = "第一段航段标识")
    private String couponnumberNo1;

    @ApiModelProperty(value = "第二段航段标识")
    private String couponnumberNo2;

    /** 第一段航班信息 */
    @ApiModelProperty(value = "第一段航班号")
    private String opcarrierFlightNo1;

    @ApiModelProperty(value = "第一段航班日期当地时间开始 格式：2024-03-02")
    private String departureDateNo1Begin;

    @ApiModelProperty(value = "起飞航点")
    private String departureAirportcode;

    @ApiModelProperty(value = "第一段计划起飞北京时间 格式：2024-03-12 15:45:00")
    private String stdNo1;

    @ApiModelProperty(value = "第一段出发机场时差")
    private Integer depTimeDiffNo1;

    @ApiModelProperty(value = "第一段到达机场三字码")
    private String arrivalAirportcodeNo1;

    @ApiModelProperty(value = "第一段计划到达北京时间 格式：2024-03-12 17:15:00")
    private String staNo1;

    @ApiModelProperty(value = "第一段到达机场时差")
    private Integer arrTimeDiffNo1;

    @ApiModelProperty(value = "机场连线标识1 D:国内 I:伙计")
    private String airportLinkSignNo1;

    @ApiModelProperty(value = "第一段承运航司")
    private String opcarrierAirlinecodeNo1;

    @ApiModelProperty(value = "第一段客票状态")
    private String couponstatusNo1;

    @ApiModelProperty(value = "第一段换开标识")
    private String exchangedFlagNo1;

    @ApiModelProperty(value = "第一段退票标识")
    private String trfdFlagNo1;

    /** 第二段航班信息 */
    @ApiModelProperty(value = "第二段航班号")
    private String opcarrierFlightNo2;

    @ApiModelProperty(value = "第二段航班日期当地时间开始 格式：2024-03-02")
    private String departureDateNo2Begin;

    @ApiModelProperty(value = "第二段起飞机场三字码")
    private String departureAirportcodeNo2;

    @ApiModelProperty(value = "第二段计划起飞北京时间 格式：2024-03-12 15:45:00")
    private String stdNo2;

    @ApiModelProperty(value = "第二段出发机场时差")
    private Integer depTimeDiffNo2;

    @ApiModelProperty(value = "落地航点")
    private String arrivalAirportcode;

    @ApiModelProperty(value = "第二段计划到达北京时间 格式：2024-03-12 15:45:00")
    private String staNo2;

    @ApiModelProperty(value = "第二段到达机场时差")
    private Integer arrTimeDiffNo2;

    @ApiModelProperty(value = "机场连线标识2 D:国内 I:伙计")
    private String airportLinkSignNo2;

    @ApiModelProperty(value = "第二段承运航司")
    private String opcarrierAirlinecodeNo2;

    @ApiModelProperty(value = "第二段换开标识")
    private String exchangedFlagNo2;

    @ApiModelProperty(value = "第二段退票标识")
    private String trfdFlagNo2;

    /** 其他信息 */
    @ApiModelProperty(value = "特殊服务类型")
    private String specialServiceType;

    @ApiModelProperty(value = "关爱旅客类型")
    private String careTravelerType;

}
