package com.juneyaoair.oneorder.api.cuss.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: caolei
 * @Description: 会员信息
 * @Date: 2021/7/22 12:35
 * @Modified by:
 */
@Data
public class MemberResponse {

    @ApiModelProperty(value = "旅客生日")
    private String travellerBirthdate;

    @ApiModelProperty(value = "会员卡号")
    private String memberCardNo;

    @ApiModelProperty(value = "会员等级code")
    private String memberLevelCode;

    @ApiModelProperty(value = "选座会员等级code")
    private Integer seatMemberCode;

}