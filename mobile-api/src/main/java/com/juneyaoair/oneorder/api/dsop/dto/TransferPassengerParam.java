package com.juneyaoair.oneorder.api.dsop.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 中转旅客参数
 * <AUTHOR>
 */
@Data
public class TransferPassengerParam {

    @ApiModelProperty(value = "当前页数。默认为1", required = true)
    private Integer pageNum;

    @ApiModelProperty(value = "每页查询行数。默认为50，最大为10000", required = true)
    private Integer pageSize;

    @ApiModelProperty(value = "航班组标识 0：中转数据")
    private String flightsGroupFlag;

    @ApiModelProperty(value = "是否24小时内衔接 1：24小时内中转的数据")
    private String is24hoursLinkup;

    @ApiModelProperty(value = "第一段航班日期北京时间开始 格式：yyyy-MM-dd")
    private String departureDateCnNo1Begin;

    @ApiModelProperty(value = "第一段航班日期当地时间开始 格式：yyyy-MM-dd")
    private String departureDateNo1Begin;

    @ApiModelProperty(value = "第二段航班日期北京时间开始 格式：yyyy-MM-dd")
    private String departureDateCnNo2Begin;

    @ApiModelProperty(value = "第二段航班日期当地时间开始 格式：yyyy-MM-dd")
    private String departureDateNo2Begin;

    @ApiModelProperty(value = "护照号")
    private String passportNumber;

}
