package com.juneyaoair.oneorder.api.cuss.dto.ume;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date Created in 2018/7/2 13:29
 */
@Data
public class UMEBoardingPass {

    @ApiModelProperty(value = "二维码字节流")
    private UMEBdPassCode bdPassCode;

    @ApiModelProperty(value = "登机序号")
    private String boardingNo;

    @ApiModelProperty(value = "二维码图片")
    private String bdPassImg;

    @ApiModelProperty(value = "登机牌样式")
    private UMEBoardingPassStyle boardingPassStyle;

    @ApiModelProperty(value = "登机牌使用截止时间")
    private UMEBpDeadline bpDeadline;

    @ApiModelProperty(value = "电子登机牌使用说明")
    private List<UMEBpInstruction> instructions;

    @ApiModelProperty(value = "是否支持电子登机牌0：不支持；1：支持")
    private Integer isSupport;

    @ApiModelProperty(value = "使用说明的title")
    private String title;

    @ApiModelProperty(value = "电子登机牌的状态")
    private String status;

    @ApiModelProperty(value = "电子安检章地址")
    private String statusImgUrl;

    @ApiModelProperty(value = "刷新安检状态时间间隔")
    private Integer refreshInteval;

}
