package com.juneyaoair.oneorder.api.basic.service;

import com.juneyaoair.flightbasic.pass.DeletePassParam;
import com.juneyaoair.flightbasic.pass.ExpirePassParam;
import com.juneyaoair.flightbasic.pass.GetPassParam;
import com.juneyaoair.flightbasic.pass.PassInfoParam;
import com.juneyaoair.flightbasic.pass.PassInfoResult;
import com.juneyaoair.flightbasic.pass.PassUpdateParam;
import com.juneyaoair.flightbasic.pass.PassUpdateResult;
import com.juneyaoair.flightbasic.pass.RegisterPassParam;
import com.juneyaoair.flightbasic.pass.SavePassParam;
import com.juneyaoair.flightbasic.pass.UpdatePassParam;

/**
 * PassService
 * <AUTHOR>
 */
public interface BasicPassService {

    /**
     * 生成Pass数据
     * @param getPassParam
     * @return
     */
    String getPass(GetPassParam getPassParam);

    /**
     * 生成过期Pass数据
     * @param expirePassParam
     * @return
     */
    String getExpirePass(ExpirePassParam expirePassParam);

    /**
     * 记录创建、更新Pass参数
     * @param savePassParam
     * @return
     */
    void savePassParam(SavePassParam savePassParam);

    /**
     * 注册Pass
     * @param registerPassParam
     * @return
     */
    void registerPass(RegisterPassParam registerPassParam);

    /**
     * 删除Pass
     * @param deletePassParam
     * @return
     */
    void deletePass(DeletePassParam deletePassParam);

    /**
     * 查询存在更新的Pass清单
     * @param passUpdateParam
     * @return
     */
    PassUpdateResult getUpdatePassList(PassUpdateParam passUpdateParam);

    /**
     * 更新Pass
     * @param updatePassParam
     * @return
     */
    void updatePass(UpdatePassParam updatePassParam);

    /**
     * 查询Pass信息
     * @param passInfoParam
     * @return
     */
    PassInfoResult getPassInfo(PassInfoParam passInfoParam);

}
