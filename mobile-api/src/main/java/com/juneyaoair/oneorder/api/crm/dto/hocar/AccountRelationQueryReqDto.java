package com.juneyaoair.oneorder.api.crm.dto.hocar;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2024/1/2 14:03
 */
@Data
@Builder
@ApiModel(value =  "AccountRelationQueryReqDto",description = "吉祥汽车账户DTO")
public class AccountRelationQueryReqDto {
    @ApiModelProperty(value = "账号类型: 吉祥航空账号:HO;吉祥汽车账号:CAR")
    @JsonProperty("AccountType")
    private String accountType;
    @ApiModelProperty(value = "账号值")
    @JsonProperty("AccountValue")
    private String accountValue;
}
