package com.juneyaoair.oneorder.api.crm.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 会员账号信息
 * <AUTHOR>
 */
@Data
public class MemberAccountInfo {

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "性别")
    private String sex;

    @ApiModelProperty(value = "会员ID")
    private String ffpId;

    @ApiModelProperty(value = "会员卡号")
    private String ffpNo;

    @ApiModelProperty(value = "手机号")
    private String memberTel;

    @ApiModelProperty(value = "电子邮箱")
    private String email;

    @ApiModelProperty(value = "会员卡号")
    private String headImageUrl;

    @ApiModelProperty(value = "账号是否启用状态 true:启用 false:关闭")
    private boolean accountStatus;

}
