package com.juneyaoair.oneorder.api.cuss.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 接口报文返回dto基类，此类作为公用结构模板
 * 
 * <AUTHOR>
 * @date 2018年3月8日 下午4:53:06
 * @version 1.0
 */
@Data
public class CussBaseResult<T> {

	/** 响应码规范改造 */
	@ApiModelProperty(value = "返回编码 1000:成功 其他：失败")
	private String code;

	@ApiModelProperty(value = "返回结果描述")
	private String des;

	@ApiModelProperty(value = "返回编码，非1001为错误返回，错误原因查看msg")
	private String resultCode;

	@ApiModelProperty(value = "返回结果描述，正确 ok")
	private String errorMsg;

	@ApiModelProperty(value = "traceId")
	private String traceId;

	@ApiModelProperty(value = "返回结果")
	private T result;

}
