package com.juneyaoair.oneorder.api.dsop.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 大数据分页数据返回
 * <AUTHOR>
 */
@Data
public class DsopPageResult<T> {

    @ApiModelProperty(value = "响应标识码。成功：SUCCESS，其他均为失败")
    private String code;

    @ApiModelProperty(value = "响应消息")
    private String message;

    @ApiModelProperty(value = "当前页数")
    private Integer pageNum;

    @ApiModelProperty(value = "每页查询行数")
    private Integer pageSize;

    @ApiModelProperty(value = "总记录数")
    private Integer total;

    @ApiModelProperty(value = "总页数")
    private Integer totalPage;

    @ApiModelProperty(value = "当前页数据集")
    private List<T> dataList;

}
