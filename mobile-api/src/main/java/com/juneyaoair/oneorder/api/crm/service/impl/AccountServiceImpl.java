package com.juneyaoair.oneorder.api.crm.service.impl;

import com.google.gson.reflect.TypeToken;
import com.juneyaoair.mobile.exception.MultiLangServiceException;
import com.juneyaoair.mobile.exception.errorcode.CommonErrorCode;
import com.juneyaoair.mobile.exception.PlatServiceException;
import com.juneyaoair.oneorder.api.common.CommonService;
import com.juneyaoair.oneorder.api.crm.config.CrmConfig;
import com.juneyaoair.oneorder.api.crm.constant.CrmUrlConstant;
import com.juneyaoair.oneorder.api.crm.dto.*;
import com.juneyaoair.oneorder.api.crm.enums.CrmCommonEnum;
import com.juneyaoair.oneorder.api.crm.service.IAccountService;
import com.juneyaoair.oneorder.api.crm.utils.CRMReqUtil;
import com.juneyaoair.oneorder.common.dto.BizDto;
import com.juneyaoair.oneorder.mobile.dto.ChannelInfo;
import com.juneyaoair.oneorder.tools.utils.HoAirGsonUtil;
import com.juneyaoair.oneorder.tools.utils.HttpResult;
import com.juneyaoair.oneorder.tools.utils.HttpUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;

/**
 * <AUTHOR>
 * @description
 * @date 2024/10/22 16:19
 */
@Service
public class AccountServiceImpl implements IAccountService {
    @Autowired
    private CrmConfig crmConfig;
    @Autowired
    private CommonService commonService;

    @Override
    public MultiLanguageRegisterAccountResultDto register(CrmRequestDto<MultiLanguageRegisterAccountReqDto> crmRequestDto) {
        String url = crmConfig.getCrmAccountOpenApiUrl() + CrmUrlConstant.REGIST_ACCOUNT_BY_MULTILANGUAGE;
        HttpResult httpResult = HttpUtil.doPostClient(crmRequestDto, url);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail(CommonErrorCode.SYSTEM_ERROR);
        }
        if (StringUtils.isBlank(httpResult.getResponse())) {
            throw MultiLangServiceException.fail(CommonErrorCode.SYSTEM_ERROR);
        }
        Type type = new TypeToken<CrmResponseDto<MultiLanguageRegisterAccountResultDto>>() {
        }.getType();
        CrmResponseDto<MultiLanguageRegisterAccountResultDto> ptCRMResponse = HoAirGsonUtil.fromJson(httpResult.getResponse(), type);
        if (ptCRMResponse == null) {
            throw MultiLangServiceException.fail(CommonErrorCode.BUSINESS_ERROR);
        }
        if (CrmCommonEnum.SUC0.getCode() != ptCRMResponse.getCode()) {
            if (CrmCommonEnum.FAIL302017.getCode() == ptCRMResponse.getCode()) {
                throw PlatServiceException.fail(ptCRMResponse.getDesc());
            }
            if (CrmCommonEnum.FAIL302018.getCode() == ptCRMResponse.getCode()) {
                throw PlatServiceException.fail(ptCRMResponse.getDesc());
            }
            throw PlatServiceException.fail(ptCRMResponse.getDesc());
        }
        return ptCRMResponse.getData();
    }

    @Override
    public QueryCardNoByDataTypeResultDto queryCardNoByDataType(CrmRequestDto<QueryCardNoByDataTypeReqDto> crmRequestDto) {
        String url = crmConfig.getCrmAccountOpenApiUrl() + CrmUrlConstant.QUERY_CARD_NO_BY_DATATYPE;
        HttpResult httpResult = HttpUtil.doPostClient(crmRequestDto, url);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail(CommonErrorCode.SYSTEM_ERROR);
        }
        if (StringUtils.isBlank(httpResult.getResponse())) {
            throw new MultiLangServiceException("CRM请求异常:" + crmRequestDto.getRandomCode());
        }
        Type type = new TypeToken<CrmResponseDto<QueryCardNoByDataTypeResultDto>>() {
        }.getType();
        CrmResponseDto<QueryCardNoByDataTypeResultDto> ptCRMResponse = HoAirGsonUtil.fromJson(httpResult.getResponse(), type);
        if (ptCRMResponse == null) {
            throw new MultiLangServiceException("数据转换异常:" + crmRequestDto.getRandomCode());
        }
        if (CrmCommonEnum.SUC0.getCode() != ptCRMResponse.getCode()) {
            throw MultiLangServiceException.fail("查询失败");
        }
        QueryCardNoByDataTypeResultDto queryCardNoByDataTypeResultDto = ptCRMResponse.getData();
        if (StringUtils.isBlank(queryCardNoByDataTypeResultDto.getMemberId())) {
            throw MultiLangServiceException.fail(CommonErrorCode.ACCOUNT_NOT_EXIST);
        }
        return queryCardNoByDataTypeResultDto;
    }

    @Override
    public LoginPasswordCheckResultDto loginPasswordCheck(CrmRequestDto<LoginPasswordCheckReqDto> crmRequestDto) {
        String url = crmConfig.getCrmAccountOpenApiUrl() + CrmUrlConstant.LOGIN_PASSWORD_CHECK;
        HttpResult httpResult = HttpUtil.doPostClient(crmRequestDto, url);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail(CommonErrorCode.SYSTEM_ERROR);
        }
        if (StringUtils.isBlank(httpResult.getResponse())) {
            throw new MultiLangServiceException("CRM请求异常:" + crmRequestDto.getRandomCode());
        }
        Type type = new TypeToken<CrmResponseDto<LoginPasswordCheckResultDto>>() {
        }.getType();
        CrmResponseDto<LoginPasswordCheckResultDto> ptCRMResponse = HoAirGsonUtil.fromJson(httpResult.getResponse(), type);
        if (ptCRMResponse == null) {
            throw new MultiLangServiceException("数据转换异常:" + crmRequestDto.getRandomCode());
        }
        if (CrmCommonEnum.SUC0.getCode() != ptCRMResponse.getCode()) {
            throw MultiLangServiceException.fail("登录失败");
        }
        return ptCRMResponse.getData();
    }

    @Override
    public ResetLoginPasswordResultDto resetLoginPasswordV2(CrmRequestDto<ResetLoginPasswordReqDto> crmRequestDto) {
        String url = crmConfig.getCrmMemberOpenApiUrl() + CrmUrlConstant.RESET_LOGIN_PASSWORD_V2;
        HttpResult httpResult = HttpUtil.doPostClient(crmRequestDto, url);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail(CommonErrorCode.SYSTEM_ERROR);
        }
        if (StringUtils.isBlank(httpResult.getResponse())) {
            throw new MultiLangServiceException("CRM请求异常:" + crmRequestDto.getRandomCode());
        }
        Type type = new TypeToken<CrmResponseDto<ResetLoginPasswordResultDto>>() {
        }.getType();
        CrmResponseDto<ResetLoginPasswordResultDto> ptCRMResponse = HoAirGsonUtil.fromJson(httpResult.getResponse(), type);
        if (ptCRMResponse == null) {
            throw new MultiLangServiceException("数据转换异常:" + crmRequestDto.getRandomCode());
        }
        if (CrmCommonEnum.SUC0.getCode() != ptCRMResponse.getCode()) {
            throw PlatServiceException.fail(ptCRMResponse.getDesc());
        }
        return ptCRMResponse.getData();
    }

    @Override
    public QueryAccountByMemberIdResultDto queryAccountByMemberId(BizDto bizDto, String memberId) {
        ChannelInfo channelInfo = commonService.findChannelInfo(bizDto.getHeadChannelCode());
        QueryAccountByMemberIdReqDto queryAccountByMemberIdReqDto = new QueryAccountByMemberIdReqDto();
        queryAccountByMemberIdReqDto.setMemberId(memberId);
        CrmRequestDto<QueryAccountByMemberIdReqDto> crmRequestDto = CRMReqUtil.buildCrmRequestDto(channelInfo.getChannelCode(), channelInfo.getChannelPwd(), bizDto.getIp(), queryAccountByMemberIdReqDto);
        String url = crmConfig.getCrmAccountOpenApiUrl() + CrmUrlConstant.QUERY_ACCOUNT_BY_MEMBER_ID;
        HttpResult httpResult = HttpUtil.doPostClient(crmRequestDto, url);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail(CommonErrorCode.SYSTEM_ERROR);
        }
        if (StringUtils.isBlank(httpResult.getResponse())) {
            throw new MultiLangServiceException("CRM请求异常:" + crmRequestDto.getRandomCode());
        }
        Type type = new TypeToken<CrmResponseDto<QueryAccountByMemberIdResultDto>>() {
        }.getType();
        CrmResponseDto<QueryAccountByMemberIdResultDto> ptCRMResponse = HoAirGsonUtil.fromJson(httpResult.getResponse(), type);
        if (ptCRMResponse == null) {
            throw new MultiLangServiceException("数据转换异常:" + crmRequestDto.getRandomCode());
        }
        if (CrmCommonEnum.SUC0.getCode() != ptCRMResponse.getCode()) {
            throw MultiLangServiceException.fail("账户查询异常");
        }
        QueryAccountByMemberIdResultDto queryAccountByMemberIdResultDto = ptCRMResponse.getData();
        if (!"N".equals(queryAccountByMemberIdResultDto.getIsAccountClosed())) {
            throw MultiLangServiceException.fail("账户状态异常，请联系客服核实");
        }
        return ptCRMResponse.getData();
    }
}
