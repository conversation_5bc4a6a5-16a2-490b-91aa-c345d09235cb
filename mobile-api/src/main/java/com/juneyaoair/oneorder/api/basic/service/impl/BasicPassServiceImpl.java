package com.juneyaoair.oneorder.api.basic.service.impl;

import com.juneyaoair.flightbasic.common.BaseRequestDTO;
import com.juneyaoair.flightbasic.common.BaseResultDTO;
import com.juneyaoair.flightbasic.feign.FlightBasicProviderClient;
import com.juneyaoair.flightbasic.pass.DeletePassParam;
import com.juneyaoair.flightbasic.pass.ExpirePassParam;
import com.juneyaoair.flightbasic.pass.GetPassParam;
import com.juneyaoair.flightbasic.pass.PassInfoParam;
import com.juneyaoair.flightbasic.pass.PassInfoResult;
import com.juneyaoair.flightbasic.pass.PassUpdateParam;
import com.juneyaoair.flightbasic.pass.PassUpdateResult;
import com.juneyaoair.flightbasic.pass.RegisterPassParam;
import com.juneyaoair.flightbasic.pass.SavePassParam;
import com.juneyaoair.flightbasic.pass.UpdatePassParam;
import com.juneyaoair.flightbasic.utils.BaseRequestUtil;
import com.juneyaoair.oneorder.api.basic.service.BasicPassService;
import com.juneyaoair.oneorder.common.dto.enums.ChannelCodeEnum;
import com.juneyaoair.oneorder.util.ResultUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * PassService
 * <AUTHOR>
 */
@Service
public class BasicPassServiceImpl implements BasicPassService {

    @Resource
    private FlightBasicProviderClient flightBasicProviderClient;

    @Override
    public String getPass(GetPassParam getPassParam) {
        BaseRequestDTO<GetPassParam> baseRequest = BaseRequestUtil.createRequest(getPassParam, ChannelCodeEnum.MOBILE.getChannelCode());
        BaseResultDTO<String> baseResult = flightBasicProviderClient.getPass(baseRequest);
        return ResultUtils.getFlightBasicResult(baseResult);
    }

    @Override
    public String getExpirePass(ExpirePassParam expirePassParam) {
        BaseRequestDTO<ExpirePassParam> baseRequest = BaseRequestUtil.createRequest(expirePassParam, ChannelCodeEnum.MOBILE.getChannelCode());
        BaseResultDTO<String> baseResult = flightBasicProviderClient.getExpirePass(baseRequest);
        return ResultUtils.getFlightBasicResult(baseResult);
    }

    @Override
    public void savePassParam(SavePassParam savePassParam) {
        BaseRequestDTO<SavePassParam> baseRequest = BaseRequestUtil.createRequest(savePassParam, ChannelCodeEnum.MOBILE.getChannelCode());
        BaseResultDTO<String> baseResult = flightBasicProviderClient.savePassParam(baseRequest);
        ResultUtils.getFlightBasicResult(baseResult);
    }

    @Override
    public void registerPass(RegisterPassParam registerPassParam) {
        BaseRequestDTO<RegisterPassParam> baseRequest = BaseRequestUtil.createRequest(registerPassParam, ChannelCodeEnum.MOBILE.getChannelCode());
        BaseResultDTO<String> baseResult = flightBasicProviderClient.registerPass(baseRequest);
        ResultUtils.getFlightBasicResult(baseResult);
    }

    @Override
    public void deletePass(DeletePassParam deletePassParam) {
        BaseRequestDTO<DeletePassParam> baseRequest = BaseRequestUtil.createRequest(deletePassParam, ChannelCodeEnum.MOBILE.getChannelCode());
        BaseResultDTO<String> baseResult = flightBasicProviderClient.deletePass(baseRequest);
        ResultUtils.getFlightBasicResult(baseResult);
    }

    @Override
    public PassUpdateResult getUpdatePassList(PassUpdateParam passUpdateParam) {
        BaseRequestDTO<PassUpdateParam> baseRequest = BaseRequestUtil.createRequest(passUpdateParam, ChannelCodeEnum.MOBILE.getChannelCode());
        BaseResultDTO<PassUpdateResult> baseResult = flightBasicProviderClient.getUpdatePassList(baseRequest);
        return ResultUtils.getFlightBasicResult(baseResult);
    }

    @Override
    public void updatePass(UpdatePassParam updatePassParam) {
        BaseRequestDTO<UpdatePassParam> baseRequest = BaseRequestUtil.createRequest(updatePassParam, ChannelCodeEnum.MOBILE.getChannelCode());
        BaseResultDTO<String> baseResult = flightBasicProviderClient.updatePass(baseRequest);
        ResultUtils.getFlightBasicResult(baseResult);
    }

    @Override
    public PassInfoResult getPassInfo(PassInfoParam passInfoParam) {
        BaseRequestDTO<PassInfoParam> baseRequest = BaseRequestUtil.createRequest(passInfoParam, ChannelCodeEnum.MOBILE.getChannelCode());
        BaseResultDTO<PassInfoResult> baseResult = flightBasicProviderClient.getPassInfo(baseRequest);
        return ResultUtils.getFlightBasicResult(baseResult);
    }

}
