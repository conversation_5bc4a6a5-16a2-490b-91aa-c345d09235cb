package com.juneyaoair.oneorder.api.cuss.dto;

import com.juneyaoair.oneorder.common.dto.RequestInterface;
import com.juneyaoair.oneorder.common.dto.enums.ChannelCodeEnum;
import com.juneyaoair.oneorder.common.enums.LanguageEnum;
import com.juneyaoair.oneorder.common.util.IPUtil;
import com.juneyaoair.oneorder.core.context.SecurityContextHolder;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

/**
 * 接口报文返回dto基类，此类作为公用结构模板
 * 
 * <AUTHOR>
 * @date 2018年3月8日 下午4:53:06
 * @version 1.0
 */
@Data
public class CussBaseRequest<T> {

	@ApiModelProperty(value = "渠道", required = true)
	@NotBlank(message = "渠道不能为空")
	private String channelCode;

	@ApiModelProperty(value = "请求IP地址", required = true)
	@NotBlank(message = "请求IP地址不能为空")
	private String ip;

	/**
	 * @see LanguageEnum
	 */
	@ApiModelProperty(value = "语言 LanguageEnum（发送电子邮件使用 默认：简体中文）")
	private LanguageEnum language;

	@ApiModelProperty(value = "接口版本号", required = true)
	@NotBlank(message = "接口版本号不能为空")
	private String version;

	@Valid
	@ApiModelProperty(value = "具体接口报文 请求参数，详情参考接口文档")
	private T request;


	/**
	 * 生成基础请求参数
	 * @param t
	 * @return
	 * @param <T>
	 */
	public static <T> CussBaseRequest<T> createRequest(T t){
		CussBaseRequest<T> baseRequest = new CussBaseRequest<>();
		baseRequest.setRequest(t);
		String originIp = SecurityContextHolder.getOriginIp();
		baseRequest.setIp(StringUtils.isBlank(originIp) ? IPUtil.getLocalIp() : originIp);
		String version = SecurityContextHolder.getVersion();
		baseRequest.setVersion(StringUtils.isBlank(version) ? "1.0" : version);
		LanguageEnum language = SecurityContextHolder.getLanguage();
		baseRequest.setLanguage(null == language ? LanguageEnum.ZH_CN : language);
		String channelCode = SecurityContextHolder.getChannelCode();
		baseRequest.setChannelCode(StringUtils.isBlank(channelCode) ? ChannelCodeEnum.MOBILE.getChannelCode() : channelCode);
		return baseRequest;
	}
}
