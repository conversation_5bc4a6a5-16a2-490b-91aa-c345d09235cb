package com.juneyaoair.oneorder.api.cuss.dto.ume;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 旅客信息
 * <AUTHOR>
 * @version 1.0
 * @date Created in 2018/7/2 13:30
 */
@Data
public class UMEPassengerInfo {

    @ApiModelProperty(value = "舱位")
    private String clazz;

    @ApiModelProperty(value = "行程序号")
    private String tourIndex;

    @ApiModelProperty(value = "常客卡号")
    private String ffpNo;

    @ApiModelProperty(value = "常客卡类型")
    private String ffpType;

    @ApiModelProperty(value = "值机完成座位号")
    private String paSeatNum;

    @ApiModelProperty(value = "舱位")
    private String passengerName;

    @ApiModelProperty(value = "票号")
    private String tktNum;

}
