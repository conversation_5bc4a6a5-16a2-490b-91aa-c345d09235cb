package com.juneyaoair.oneorder.api.cuss.dto.ume;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date Created in 2018/7/2 13:47
 */
@Data
public class UMEBoardingPassStyle {

    @ApiModelProperty(value = "背景颜色")
    private String backgroundColor;

    @ApiModelProperty(value = "标题字体颜色")
    private String titleFontColor;

    @ApiModelProperty(value = "内容信息字体颜色")
    private String contentFontColor;

    @ApiModelProperty(value = "电子登机牌标题栏背景颜色")
    private String topBarColor;

    @ApiModelProperty(value = "标题栏字体颜色")
    private String topBarFontColor;

    @ApiModelProperty(value = "底部按钮颜色")
    private String buttonColor;

    @ApiModelProperty(value = "底部按钮字体颜色")
    private String buttonFontColor;

    @ApiModelProperty(value = "航空公司logo地址")
    private String airlineImgUrl;

    @ApiModelProperty(value = "航空公司名称")
    private String airlineName;

}
