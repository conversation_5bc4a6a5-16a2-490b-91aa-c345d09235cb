package com.juneyaoair.oneorder.api.cuss.service;

import com.juneyaoair.oneorder.api.cuss.dto.MemberRequest;
import com.juneyaoair.oneorder.api.cuss.dto.MemberResponse;
import com.juneyaoair.oneorder.api.cuss.dto.ume.BoardingPassRequest;
import com.juneyaoair.oneorder.api.cuss.dto.ume.BoardingPassResponse;

/**
 * 值机选座服务
 * <AUTHOR>
 */
public interface CussService {

    /**
     * 查询登机牌信息
     * @param boardingPassRequest
     * @return
     */
    BoardingPassResponse getBoardingPass(BoardingPassRequest boardingPassRequest);

    /**
     * 获取会员相关信息（基于票号或证件号）
     * @param memberRequest
     * @return
     */
    MemberResponse getMemberInfo(MemberRequest memberRequest);

}
