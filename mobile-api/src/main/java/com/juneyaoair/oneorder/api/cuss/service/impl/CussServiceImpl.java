package com.juneyaoair.oneorder.api.cuss.service.impl;

import com.alibaba.fastjson.TypeReference;
import com.juneyaoair.flightbasic.common.UnifiedOrderResultEnum;
import com.juneyaoair.mobile.exception.MultiLangServiceException;
import com.juneyaoair.oneorder.api.cuss.config.CussConfig;
import com.juneyaoair.oneorder.api.cuss.constant.CussConstant;
import com.juneyaoair.oneorder.api.cuss.dto.CussBaseRequest;
import com.juneyaoair.oneorder.api.cuss.dto.CussBaseResult;
import com.juneyaoair.oneorder.api.cuss.dto.MemberRequest;
import com.juneyaoair.oneorder.api.cuss.dto.MemberResponse;
import com.juneyaoair.oneorder.api.cuss.dto.ume.BoardingPassRequest;
import com.juneyaoair.oneorder.api.cuss.dto.ume.BoardingPassResponse;
import com.juneyaoair.oneorder.api.cuss.service.CussService;
import com.juneyaoair.oneorder.util.HttpSendUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 值机选座服务
 * <AUTHOR>
 */
@Service
public class CussServiceImpl implements CussService {

    @Autowired
    private CussConfig cussConfig;

    @Override
    public BoardingPassResponse getBoardingPass(BoardingPassRequest boardingPassRequest) {
        CussBaseRequest<BoardingPassRequest> cussBaseRequest = CussBaseRequest.createRequest(boardingPassRequest);
        TypeReference<CussBaseResult<BoardingPassResponse>> typeReference = new TypeReference<CussBaseResult<BoardingPassResponse>>(){};
        String url = cussConfig.getCussUrl() + CussConstant.GET_BOARDING_PASS;
        CussBaseResult<BoardingPassResponse> cussBaseResult = HttpSendUtil.doPostClient(cussBaseRequest, url, typeReference);
        if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(cussBaseResult.getResultCode())) {
            throw MultiLangServiceException.fail(cussBaseResult.getErrorMsg());
        }
        return cussBaseResult.getResult();
    }

    @Override
    public MemberResponse getMemberInfo(MemberRequest memberRequest) {
        CussBaseRequest<MemberRequest> cussBaseRequest = CussBaseRequest.createRequest(memberRequest);
        TypeReference<CussBaseResult<MemberResponse>> typeReference = new TypeReference<CussBaseResult<MemberResponse>>(){};
        String url = cussConfig.getCussUrl() + CussConstant.GET_MEMBER_INFO;
        CussBaseResult<MemberResponse> cussBaseResult = HttpSendUtil.doPostClient(cussBaseRequest, url, typeReference);
        if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(cussBaseResult.getResultCode())) {
            throw MultiLangServiceException.fail(cussBaseResult.getErrorMsg());
        }
        return cussBaseResult.getResult();
    }

}
