package com.juneyaoair.oneorder.api.basic.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import com.google.gson.reflect.TypeToken;
import com.juneyaoair.flightbasic.advertisement.AdvertisementDto;
import com.juneyaoair.flightbasic.advertisement.AdvertisementParam;
import com.juneyaoair.flightbasic.aircraft.AircraftTypeInfo;
import com.juneyaoair.flightbasic.aircraft.AircraftTypeInfoQuery;
import com.juneyaoair.flightbasic.api.index.SaleOfficeReq;
import com.juneyaoair.flightbasic.api.index.SaleOfficeResp;
import com.juneyaoair.flightbasic.common.*;
import com.juneyaoair.flightbasic.commondto.RequestData;
import com.juneyaoair.flightbasic.commondto.ResponseCode;
import com.juneyaoair.flightbasic.commondto.ResponseData;
import com.juneyaoair.flightbasic.feign.FlightBasicConsumerClient;
import com.juneyaoair.flightbasic.feign.FlightBasicProviderClient;
import com.juneyaoair.flightbasic.feign.RouteCalendarClient;
import com.juneyaoair.flightbasic.redis.RedisKeyConstants;
import com.juneyaoair.flightbasic.request.SelectFlightCabinReq;
import com.juneyaoair.flightbasic.request.airport.AirPortInfoReqDTO;
import com.juneyaoair.flightbasic.request.city.CityInfoReqDTO;
import com.juneyaoair.flightbasic.request.country.TCountryReqDTO;
import com.juneyaoair.flightbasic.request.flightInfo.FlightInfoReqDTO;
import com.juneyaoair.flightbasic.request.notice.NoticeRequestDTO;
import com.juneyaoair.flightbasic.response.HolidayCalenderResponse;
import com.juneyaoair.flightbasic.response.SelectFlightCabinDto;
import com.juneyaoair.flightbasic.response.airline.FollowAirLineResDTO;
import com.juneyaoair.flightbasic.response.airport.AirPortInfoDTO;
import com.juneyaoair.flightbasic.response.api.HolidayCalendar;
import com.juneyaoair.flightbasic.response.city.CityInfoDTO;
import com.juneyaoair.flightbasic.response.country.TCountryDTO;
import com.juneyaoair.flightbasic.response.flightInfo.FlightInfoDTO;
import com.juneyaoair.flightbasic.response.notice.NoticeInfoResponseDTO;
import com.juneyaoair.flightbasic.response.notice.TNoticeInfoResponseDTO;
import com.juneyaoair.flightbasic.route.RouteLabelDTO;
import com.juneyaoair.flightbasic.utils.BaseRequestUtil;
import com.juneyaoair.i18n.enums.I18nDictionaryTypeEnum;
import com.juneyaoair.mobile.exception.MultiLangServiceException;
import com.juneyaoair.mobile.exception.ServiceException;
import com.juneyaoair.mobile.exception.errorcode.CommonErrorCode;
import com.juneyaoair.mobile.exception.util.HoAirIpUtil;
import com.juneyaoair.oneorder.api.basic.service.I18nDictService;
import com.juneyaoair.oneorder.api.basic.service.IBasicService;
import com.juneyaoair.oneorder.api.common.CommonService;
import com.juneyaoair.oneorder.common.dto.BizDto;
import com.juneyaoair.oneorder.common.dto.enums.ChannelCodeEnum;
import com.juneyaoair.oneorder.common.enums.LanguageEnum;
import com.juneyaoair.oneorder.core.context.SecurityContextHolder;
import com.juneyaoair.oneorder.mobile.config.RedisConstantConfig;
import com.juneyaoair.oneorder.tools.utils.HoAirGsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @ClassName BasicServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2023/6/25 8:39
 * @Version 1.0
 */
@Service
@Slf4j
public class BasicServiceImpl extends CommonService implements IBasicService {

    @Resource
    private FlightBasicConsumerClient flightBasicConsumerClient;
    @Resource
    private FlightBasicProviderClient flightBasicProviderClient;

    @Resource
    private RouteCalendarClient   routeCalendarClient;

    @Resource
    private I18nDictService i18nDictService;

    @Autowired
    private RedisConstantConfig redisConstantConfig;

    @Override
    public List<CityInfoDTO> toCatchAllCityList(String ip, String channelCode, String cityCode) {
        BizDto bizDto = new BizDto(ip, channelCode);
        CityInfoReqDTO cityInfoReqDTO = new CityInfoReqDTO();
        cityInfoReqDTO.setCityCode(cityCode);
        BaseRequestDTO<CityInfoReqDTO> cityInfoBaseReqDTO = createBaseRequestDTO(bizDto, cityInfoReqDTO);
        BaseResultDTO<List<CityInfoDTO>> allCityInfosV2 = flightBasicConsumerClient.getAllCityInfosV2(cityInfoBaseReqDTO);
        log.info("基础服务获取城市信息,请求参数:{},响应结果:{}", HoAirGsonUtil.objectToJson(cityInfoBaseReqDTO), allCityInfosV2);
        if (null == allCityInfosV2) {
            throw ServiceException.fail("基础服务调用出错");
        }
        if (!"10001".equals(allCityInfosV2.getResultCode())) {
            throw ServiceException.fail("基础服务调用出错:" + allCityInfosV2.getErrorMsg());
        }
        return allCityInfosV2.getResult();
    }

    @Override
    public List<AirPortInfoDTO> toCatchAllAirPortList(String ip, String channelCode, String airportCode, String cityCode) {
        BaseRequestDTO<AirPortInfoReqDTO> airPortInfoBaseReq = new BaseRequestDTO<>();
        airPortInfoBaseReq.setIp(ip);
        airPortInfoBaseReq.setChannelCode(channelCode);
        airPortInfoBaseReq.setUserNo("10001");
        airPortInfoBaseReq.setVersion("V1.0");

        AirPortInfoReqDTO airPortInfoReqDTO = new AirPortInfoReqDTO();
        airPortInfoReqDTO.setAirportCode(airportCode);
        airPortInfoReqDTO.setCityCode(cityCode);
        airPortInfoBaseReq.setRequest(airPortInfoReqDTO);
        BaseResultDTO<List<AirPortInfoDTO>> allAirPortInfoV2 = flightBasicConsumerClient.getAllAirPortInfoV2(airPortInfoBaseReq);
        log.info("基础服务获取机场信息,请求参数:{},响应结果:{}", HoAirGsonUtil.objectToJson(airPortInfoBaseReq), allAirPortInfoV2);
        if (null == allAirPortInfoV2) {
            throw ServiceException.fail("基础服务调用出错");
        }
        if (!"10001".equals(allAirPortInfoV2.getResultCode())) {
            throw ServiceException.fail("基础服务调用出错:" + allAirPortInfoV2.getErrorMsg());
        }
        return allAirPortInfoV2.getResult();
    }

    @Override
    public List<TCountryDTO> queryCountries(String ip, String channelCode, TCountryReqDTO tCountryReqDTO) {
        if (tCountryReqDTO != null && StringUtils.isNotEmpty(tCountryReqDTO.getCountryCode())) {
            RequestData<TCountryReqDTO> requestDTO = new RequestData<>();
            requestDTO.setOriginIp(ip);
            requestDTO.setChannelNo(channelCode);
            requestDTO.setData(tCountryReqDTO);
            ResponseData<List<TCountryDTO>> responseData = flightBasicProviderClient.countryInfoV2(requestDTO);
            if (!ResponseCode.SUCCESS.getCode().equals(responseData.getCode())) {
                throw ServiceException.fail("国家信息查询出错:" + responseData.getMessage());
            }
            return responseData.getData();
        }
        String redisKey = redisConstantConfig.getLocalRedisKey(redisConstantConfig.COMMON_COUNTRIES);
        //先走缓存结果
        String redisResult = (String) redisUtil.get(redisKey);
        if (StringUtils.isNotEmpty(redisResult)) {
            return JSON.parseObject(redisResult, new TypeToken<List<TCountryDTO>>() {
            }.getType());
        }
        //缓存中无值时去基础服务查询
        RequestData<TCountryReqDTO> requestDTO = new RequestData<>();
        requestDTO.setOriginIp(ip);
        requestDTO.setChannelNo(channelCode);
        ResponseData<List<TCountryDTO>> responseData = flightBasicProviderClient.countryInfoV2(requestDTO);
        if (!ResponseCode.SUCCESS.getCode().equals(responseData.getCode())) {
            throw ServiceException.fail("国家信息查询出错:" + responseData.getMessage());
        }
        if (CollectionUtils.isNotEmpty(responseData.getData())) {
            redisUtil.set(redisKey, JSON.toJSONString(responseData.getData()), 7 * 24 * 3600L);//默认存放7天
        }
        return responseData.getData();
    }

    @Override
    public TCountryDTO queryCountry(String countryNo) {
        TCountryReqDTO tCountryReqDTO = new TCountryReqDTO();
        tCountryReqDTO.setCountryCode(countryNo);
        List<TCountryDTO> countryList = queryCountries(HoAirIpUtil.getLocalIp(), ChannelCodeEnum.MOBILE.getChannelCode(), tCountryReqDTO);
        if (CollectionUtils.isEmpty(countryList)) {
            return null;
        }
        return countryList.get(0);
    }

    @Override
    public List<FlightInfoDTO> searchFlightInfo(BaseRequestDTO<FlightInfoReqDTO> baseRequestDTO) {
        long s = System.currentTimeMillis();
        printReqLog("", "flightBasicConsumerClient.searchflightInfoList", baseRequestDTO);
        BaseResultDTO<List<FlightInfoDTO>> baseResultDTO = flightBasicConsumerClient.searchflightInfoList(baseRequestDTO);
        printResultLog("", "flightBasicConsumerClient.searchflightInfoList", baseResultDTO, s);
        if (!WSEnum.SUCCESS.resultCode.equals(baseResultDTO.getResultCode())) {
            throw ServiceException.fail("航班信息查询出错:" + baseResultDTO.getResultInfo());
        }
        return baseResultDTO.getResult();
    }

    @Override
    public FlightInfoDTO searchSingleFlightInfo(String flightNo, String flightDate, String depAirportCode, String arrAirportCode) {
        if (StringUtils.isAnyBlank(flightNo, flightDate, depAirportCode, arrAirportCode)) {
            log.info("航班信息查询参数出错，航班号：{} 航班日期：{} 出发机场：{} 到达机场：{}", flightNo, flightDate, depAirportCode, arrAirportCode);
            return null;
        }
        FlightInfoReqDTO flightInfoReq = new FlightInfoReqDTO();
        flightInfoReq.setFlightNo(flightNo);
        flightInfoReq.setFlightDate(flightDate);
        flightInfoReq.setDepAirport(depAirportCode);
        flightInfoReq.setArrAirport(arrAirportCode);
        BaseRequestDTO<FlightInfoReqDTO> baseRequest = createBaseRequestDTO(SecurityContextHolder.getOriginIp(), SecurityContextHolder.getChannelCode(), flightInfoReq);
        List<FlightInfoDTO> flightInfoList = searchFlightInfo(baseRequest);
        if (CollectionUtils.isEmpty(flightInfoList)) {
            return null;
        }
        return flightInfoList.get(0);
    }

    @Override
    public PageInfo<AdvertisementDto> queryAdvertisementListByPage(BizDto bizDto, AdvertisementParam advertisementParam) {
        long s = System.currentTimeMillis();
        BaseReq<AdvertisementParam> baseReq = createBaseReq(bizDto, advertisementParam);
        printReqLog("", "flightBasicProviderClient.pageList", baseReq);
        BaseRespDTO<PageInfo<AdvertisementDto>> baseResultDTO = flightBasicProviderClient.pageList(baseReq);
        printResultLog("", "flightBasicProviderClient.pageList", baseResultDTO, s);
        if (!WSEnum.SUCCESS.resultCode.equals(baseResultDTO.getResultCode())) {
            throw ServiceException.fail("查询出错:" + baseResultDTO.getResultInfo());
        }
        return baseResultDTO.getObjData();
    }

    @Override
    public List<NoticeInfoResponseDTO> getAllNoticeInfo(BizDto bizDto, NoticeRequestDTO noticeRequestDTO) {
        long s = System.currentTimeMillis();
        BaseReq<NoticeRequestDTO> baseReq = createBaseReq(bizDto, noticeRequestDTO);
        printReqLog("", "flightBasicProviderClient.getAllNoticeInfo", baseReq);
        BaseResponseDTO<List<NoticeInfoResponseDTO>> baseResultDTO = flightBasicProviderClient.getAllNoticeInfo(baseReq);
        printResultLog("", "flightBasicProviderClient.getAllNoticeInfo", baseResultDTO, s);
        if (!WSEnum.SUCCESS.resultCode.equals(baseResultDTO.getResultCode())) {
            throw ServiceException.fail("查询出错:" + baseResultDTO.getResultInfo());
        }
        return baseResultDTO.getObjData();
    }

    @Override
    public TNoticeInfoResponseDTO getRichTextNoticeInfo(BizDto bizDto, NoticeRequestDTO noticeRequestDTO) {
        long s = System.currentTimeMillis();
        BaseReq<NoticeRequestDTO> baseReq = createBaseReq(bizDto, noticeRequestDTO);
        printReqLog("", "flightBasicProviderClient.getRichTextNoticeInfo", baseReq);
        BaseResponseDTO<TNoticeInfoResponseDTO> baseResultDTO = flightBasicProviderClient.getRichTextNoticeInfo(baseReq);
        printResultLog("", "flightBasicProviderClient.getRichTextNoticeInfo", baseResultDTO, s);
        if (!WSEnum.SUCCESS.resultCode.equals(baseResultDTO.getResultCode())) {
            throw ServiceException.fail("查询出错:" + baseResultDTO.getResultInfo());
        }
        return baseResultDTO.getObjData();
    }




    @Override
    public List<FollowAirLineResDTO> queryAttentionFlightList(BizDto bizDto, String ffpCardNo) {
        long s = System.currentTimeMillis();
        RequestData requestData = new RequestData();
        requestData.setChannelNo(bizDto.getHeadChannelCode());
        requestData.setFfpNo(ffpCardNo);
        requestData.setOriginIp(bizDto.getIp());
        printReqLog("", "flightBasicProviderClient.queryAttentionFlightList", requestData);
        ResponseData<List<FollowAirLineResDTO>> baseResultDTO = flightBasicProviderClient.queryAttentionFlightList(requestData);
        printResultLog("", "flightBasicProviderClient.queryAttentionFlightList", baseResultDTO, s);
        if (!ResponseCode.SUCCESS.name().equals(baseResultDTO.getCode())) {
            throw ServiceException.fail("查询出错:" + baseResultDTO.getMessage());
        }
        return baseResultDTO.getData();
    }

    @Override
    public SaleOfficeResp getSaleOffice(SaleOfficeReq data, LanguageEnum language) {
        if(language == null){
            language = LanguageEnum.ZH_CN;
        }
        data.setLanguage(com.juneyaoair.flightbasic.enums.LanguageEnum.valueOf(language.name()));
        BaseResultDTO<SaleOfficeResp> resp = flightBasicProviderClient.getSaleOffice(data);
        if (resp == null) {
            return null;
        }
        return resp.getResult();
    }

    @Override
    public String getPlaneTypeName(String planType, String language) {
        AircraftTypeInfo aircraftTypeInfo = getAircraftTypeInfo(planType);
        if (null == aircraftTypeInfo) {
            return planType;
        }
        if (null == language) {
            language = LanguageEnum.ZH_CN.name();
        }
        String translation = i18nDictService.getTranslation(I18nDictionaryTypeEnum.PLAN_TYPE_NAME, aircraftTypeInfo.getAircraftTypeCode(), language);
        return null == translation ? planType : translation;
    }

    /**
     * 获取机型信息
     * @param planType
     * @return
     */
    @Override
    public AircraftTypeInfo getAircraftTypeInfo(String planType) {
        AircraftTypeInfo aircraftTypeInfo = redisUtil.hget(RedisKeyConstants.AIRCRAFT_TYPE_INFO, planType, AircraftTypeInfo.class);
        if (null != aircraftTypeInfo) {
            return aircraftTypeInfo;
        }
        getAircraftTypeInfo();
        return redisUtil.hget(RedisKeyConstants.AIRCRAFT_TYPE_INFO, planType, AircraftTypeInfo.class);
    }

    /**
     * 获取机型信息清单
     */
    private void getAircraftTypeInfo(){
        AircraftTypeInfoQuery aircraftTypeInfoQuery = new AircraftTypeInfoQuery();
        aircraftTypeInfoQuery.setRefresh(true);
        BaseRequestDTO<AircraftTypeInfoQuery> baseRequest = BaseRequestUtil.createRequest(aircraftTypeInfoQuery);
        BaseResultDTO<List<AircraftTypeInfo>> baseResult = flightBasicProviderClient.getAircraftTypeInfo(baseRequest);
        List<AircraftTypeInfo> aircraftTypeInfoList = baseResult.getResult();
        if (CollectionUtils.isEmpty(aircraftTypeInfoList)) {
            return;
        }
        Map<String, Object> aircraftTypeMap = Maps.newHashMap();
        aircraftTypeInfoList.forEach(aircraftTypeInfo -> {
            Set<String> aircraftCodeSet = aircraftTypeInfo.getAircraftCodeSet();
            if (CollectionUtils.isEmpty(aircraftCodeSet)) {
                return;
            }
            aircraftCodeSet.forEach(planType -> aircraftTypeMap.put(planType, JSON.toJSONString(aircraftTypeInfo)));
        });
        redisUtil.hmset(RedisKeyConstants.AIRCRAFT_TYPE_INFO, aircraftTypeMap, 24 * 60 * 60L);
    }

    @Override
    public List<HolidayCalendar> toCatchHolidayCalender() {
        BaseResultDTO<HolidayCalenderResponse> holidayCalender = flightBasicProviderClient.toCatchHolidayCalender();
        if (null == holidayCalender) {
            log.error("节假日列表查询网络出错");
            throw new ServiceException(WSEnum.API_ERROR.resultInfo);
        }
        if (StringUtils.isEmpty(holidayCalender.getResultCode()) || !WSEnum.SUCCESS.resultCode.equals(holidayCalender.getResultCode())) {
            log.error("节假日列表查询出错，错误码[{}]，错误信息[{}]", holidayCalender.getResultCode(), holidayCalender.getErrorMsg());
            throw new ServiceException(holidayCalender.getErrorMsg());
        }
        return holidayCalender.getResult().getHolidayCalendarList();
    }

    @Override
    public void checkScoreRule(String channelNo, String originIp, String ffpId, String ffpNo) {
        RequestData requestData = new RequestData<>();
        requestData.setChannelNo(channelNo);
        requestData.setOriginIp(originIp);
        requestData.setFfpId(ffpId);
        requestData.setFfpNo(ffpNo);
        ResponseData responseData = flightBasicConsumerClient.checkScoreRule(requestData);
        if (ResponseCode.NO_REAL_NAME.name().equals(responseData.getCode())) {
            throw new MultiLangServiceException(CommonErrorCode.NO_REAL_NAME, CommonErrorCode.NO_REAL_NAME.getMessage());
        }
        if (ResponseCode.REAL_NAME_EXPIRED.name().equals(responseData.getCode())) {
            throw new MultiLangServiceException(CommonErrorCode.REAL_NAME_EXPIRED, CommonErrorCode.REAL_NAME_EXPIRED.getMessage());
        }
        if (!ResponseCode.SUCCESS.name().equals(responseData.getCode())) {
            throw new MultiLangServiceException(CommonErrorCode.BUSINESS_ERROR, "实名认证校验失败，请稍后再试");
        }
    }

    @Override
    public List<SelectFlightCabinDto> selectFlightCabin(SelectFlightCabinReq selectFlightCabinReq) {
        RequestData requestData = new RequestData();
        requestData.setChannelNo(ChannelCodeEnum.MOBILE.getChannelCode());
        requestData.setOriginIp(SecurityContextHolder.getOriginIp());
        requestData.setData(selectFlightCabinReq);
        ResponseData<List<SelectFlightCabinDto>>  responseData=routeCalendarClient.selectFlightCabin(requestData);
        if (WSEnum.SUCCESS.resultCode.equals(responseData.getResultCode())){
            return responseData.getData();
        }
        return Collections.emptyList();
    }

    @Override
    public List<RouteLabelDTO> queryRouteLabel(String channelCode) {
        BaseRequestDTO<Object> baseRequest = BaseRequestUtil.createRequest(null, channelCode);
        BaseResultDTO<List<RouteLabelDTO>> baseResult = flightBasicProviderClient.queryRouteLabel(baseRequest);
        if (null == baseResult) {
            log.error("查询航线标签返回空");
            throw MultiLangServiceException.fail(WSEnum.API_ERROR.resultInfo);
        }
        if (!WSEnum.SUCCESS.resultCode.equals(baseResult.getResultCode())) {
            log.error("查询航线标签出错，错误码:{}，错误信息:{}", baseResult.getResultCode(), baseResult.getErrorMsg());
            throw MultiLangServiceException.fail(baseResult.getErrorMsg());
        }
        return baseResult.getResult();
    }

    @Override
    public List<RouteLabelDTO> queryRouteLabel(String channelCode, String labelFunction) {
        List<RouteLabelDTO> routeLabelList = queryRouteLabel(channelCode);
        // 查询中转旅客标签数据
        routeLabelList = routeLabelList.stream().filter(label -> labelFunction.equals(label.getLabelFunction())).collect(Collectors.toList());
        // 过滤航线标签启用日期、渠道
        routeLabelList = filterRouteLabels(routeLabelList, channelCode);
        return routeLabelList;
    }

    /**
     * 航线标签是否启用、标签开始结束日期、渠道
     *
     * @param routeLabels
     * @param channelCode
     * @return
     */
    private List<RouteLabelDTO> filterRouteLabels(List<RouteLabelDTO> routeLabels, String channelCode) {
        routeLabels = routeLabels.stream().filter(label -> {
            // 标签启用
            boolean enable = label.isEnableStatus();
            // 当前时间在区间内
            boolean inDateRange = DateUtil.isIn(new Date(), DateUtil.parse(label.getStartDate()), DateUtil.parse(label.getEndDate()));
            // 包含渠道
            boolean inChannelRange = label.getChannelList().stream().anyMatch(channel -> channelCode.equals(channel.getChannel()));
            return enable && inChannelRange && inDateRange;
        }).collect(Collectors.toList());
        return routeLabels;
    }

}
