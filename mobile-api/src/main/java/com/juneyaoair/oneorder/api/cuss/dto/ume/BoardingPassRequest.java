package com.juneyaoair.oneorder.api.cuss.dto.ume;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 获取电子登机牌
 * <AUTHOR>
 * @version 1.0
 * @date Created in 2018/7/2 11:15
 */
@Data
public class BoardingPassRequest {

    @ApiModelProperty(value = "客票号", required = true)
    @NotBlank(message = "客票号不能为空")
    private String tktNum;

    @NotBlank(message = "航段序号不能为空")
    @ApiModelProperty(value = "航段序号", required = true)
    private String tourIndex;

    @NotBlank(message = "承运航班号不能为空")
    @ApiModelProperty(value = "承运航班号", required = true)
    private String hostFlightNo;

    @NotBlank(message = "航班日期不能为空")
    @Pattern(regexp="^[0-9]{4}-[0-9]{2}-[0-9]{2}$",message="航班日期格式只能是yyyy-MM-dd")
    @ApiModelProperty(value = "航班日期（yyyy-MM-dd）", required = true)
    private String flightDate;

    @NotBlank(message = "起飞机场三字码不能为空")
    @ApiModelProperty(value = "起飞机场三字码", required = true)
    private String deptCode;

    @NotBlank(message = "到达机场三字码不能为空")
    @ApiModelProperty(value = "到达机场三字码", required = true)
    private String destCode;

    @NotBlank(message = "客票状态不能为空")
    @ApiModelProperty(value = "客票状态 票面状态信息 USED/FLOWN OPEN FOR USE VOID CHECK IN", required = true)
    private String ticketStatus;

    @NotBlank(message = "乘客姓名不能为空")
    @ApiModelProperty(value = "乘客姓名", required = true)
    private String passengerName;

    @ApiModelProperty(value = "是否刷新")
    private Boolean refresh;

}
