package com.juneyaoair.oneorder.api.cuss.dto.ume;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date Created in 2018/7/2 13:37
 */
@Data
public class UMECheckInTravelInfo {

    @ApiModelProperty(value = "航空公司2字码")
    private String airline;

    @ApiModelProperty(value = "登机口")
    private String bdGate;

    @ApiModelProperty(value = "登机时间")
    private String bdTime;

    @ApiModelProperty(value = "出发三字码")
    private String deptAirportCode;

    @ApiModelProperty(value = "出发机场名称")
    private String deptAirportName;

    @ApiModelProperty(value = "出发城市中文名")
    private String deptCityName;

    @ApiModelProperty(value = "出发机场航站楼")
    private String deptTerminal;

    @ApiModelProperty(value = "到达三字码")
    private String destAirportCode;

    @ApiModelProperty(value = "到达机场名称")
    private String destAirportName;

    @ApiModelProperty(value = "到达城市中文名")
    private String destCityName;

    @ApiModelProperty(value = "到达航站楼")
    private String destTerminal;

    @ApiModelProperty(value = "航班日期")
    private String flightDate;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "承运航班号")
    private String hostFlightNo;

}
