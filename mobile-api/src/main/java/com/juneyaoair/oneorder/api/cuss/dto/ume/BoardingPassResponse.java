package com.juneyaoair.oneorder.api.cuss.dto.ume;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @version 1.0
 * @date Created in 2018/7/2 13:14
 */
@Data
public class BoardingPassResponse {

    @ApiModelProperty(value = "电子登机牌信息")
    private UMEBoardingPass boardingpass;

    @ApiModelProperty(value = "行程信息")
    private UMECheckInTravelInfo checkinTravelInfo;

    @ApiModelProperty(value = "旅客信息")
    private UMEPassengerInfo passengerInfo;

}
