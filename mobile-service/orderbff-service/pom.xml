<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.juneyaoair.oneorder</groupId>
        <artifactId>mobile-service</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>orderbff-service</artifactId>
    <packaging>jar</packaging>

    <properties>
        <groovy.version>3.0.8</groovy.version>
        <oneorder.mybatis>1.0.1-SNAPSHOT</oneorder.mybatis>
    </properties>

    <dependencies>
        <!--actuator 完善监控信息-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commons-configuration</artifactId>
                    <groupId>commons-configuration</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>woodstox-core</artifactId>
                    <groupId>com.fasterxml.woodstox</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-sleuth</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>HdrHistogram</artifactId>
                    <groupId>org.hdrhistogram</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.module</groupId>
            <artifactId>jackson-module-kotlin</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>annotations</artifactId>
                    <groupId>org.jetbrains</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.github.ulisesbocchio</groupId>
            <artifactId>jasypt-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <artifactId>asm</artifactId>
                    <groupId>org.ow2.asm</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.spockframework</groupId>
            <artifactId>spock-spring</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.spockframework</groupId>
            <artifactId>spock-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy-all</artifactId>
            <type>pom</type>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.jacoco</groupId>
            <artifactId>jacoco-maven-plugin</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>asm</artifactId>
                    <groupId>org.ow2.asm</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-beanutils</artifactId>
                    <groupId>commons-beanutils</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-collections</artifactId>
                    <groupId>commons-collections</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-lang</artifactId>
                    <groupId>commons-lang</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>doxia-sink-api</artifactId>
                    <groupId>org.apache.maven.doxia</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>maven-artifact</artifactId>
                    <groupId>org.apache.maven</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>maven-artifact-manager</artifactId>
                    <groupId>org.apache.maven</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>maven-model</artifactId>
                    <groupId>org.apache.maven</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>maven-plugin-api</artifactId>
                    <groupId>org.apache.maven</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>maven-repository-metadata</artifactId>
                    <groupId>org.apache.maven</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>maven-settings</artifactId>
                    <groupId>org.apache.maven</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>plexus-component-annotations</artifactId>
                    <groupId>org.codehaus.plexus</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>plexus-container-default</artifactId>
                    <groupId>org.codehaus.plexus</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>plexus-interpolation</artifactId>
                    <groupId>org.codehaus.plexus</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>plexus-utils</artifactId>
                    <groupId>org.codehaus.plexus</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>xml-apis</artifactId>
                    <groupId>xml-apis</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.travelsky</groupId>
            <artifactId>ebuildapi</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>checker-qual</artifactId>
                    <groupId>org.checkerframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>error_prone_annotations</artifactId>
                    <groupId>com.google.errorprone</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 项目版本控制 ################################################-->
        <dependency>
            <groupId>com.juneyaoair.oneorder</groupId>
            <artifactId>mobile-config-apollo</artifactId>
        </dependency>
        <dependency>
            <groupId>com.juneyaoair.oneorder</groupId>
            <artifactId>component-logback</artifactId>
        </dependency>
        <dependency>
            <groupId>com.juneyaoair.oneorder</groupId>
            <artifactId>component-security</artifactId>
        </dependency>

        <dependency>
            <groupId>com.juneyaoair.oneorder</groupId>
            <artifactId>component-exception</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.checkerframework</groupId>
                    <artifactId>checker-qual</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.errorprone</groupId>
                    <artifactId>error_prone_annotations</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.juneyaoair.oneorder</groupId>
            <artifactId>product-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.juneyaoair.oneorder</groupId>
                    <artifactId>component-restresult</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.juneyaoair.oneorder</groupId>
            <artifactId>bundle-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.juneyaoair.oneorder</groupId>
                    <artifactId>component-restresult</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.juneyaoair.oneorder</groupId>
            <artifactId>order-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jsqlparser</artifactId>
                    <groupId>com.github.jsqlparser</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mybatis</artifactId>
                    <groupId>org.mybatis</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.juneyaoair.oneorder</groupId>
            <artifactId>component-restresult</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>com.juneyaoair.oneorder</groupId>
            <artifactId>mobile-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.juneyaoair.oneorder</groupId>
                    <artifactId>component-restresult</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.juneyaoair.oneorder</groupId>
                    <artifactId>order-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.juneyaoair.oneorder</groupId>
            <artifactId>component-tools</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commons-fileupload</artifactId>
                    <groupId>commons-fileupload</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>javassist</artifactId>
                    <groupId>org.javassist</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>poi-ooxml-schemas</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--hutool-all工具包-->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.juneyaoair.oneorder</groupId>
            <artifactId>coupon-client</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <groupId>com.juneyaoair.oneorder</groupId>
                    <artifactId>component-restresult</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.juneyaoair.oneorder</groupId>
                    <artifactId>order-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.juneyaoair.oneorder</groupId>
            <artifactId>service-support</artifactId>
        </dependency>
        <dependency>
            <groupId>com.juneyaoair.oneorder</groupId>
            <artifactId>component-swagger</artifactId>
        </dependency>
        <dependency>
            <groupId>com.juneyaoair.horder</groupId>
            <artifactId>horder-api-common</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>asm</artifactId>
                    <groupId>org.ow2.asm</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-fileupload</artifactId>
                    <groupId>commons-fileupload</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>json</artifactId>
                    <groupId>org.json</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>ofdrw-converter</artifactId>
                    <groupId>org.ofdrw</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>ofdrw-core</artifactId>
                    <groupId>org.ofdrw</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>ofdrw-font</artifactId>
                    <groupId>org.ofdrw</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>ofdrw-gm</artifactId>
                    <groupId>org.ofdrw</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>ofdrw-graphics2d</artifactId>
                    <groupId>org.ofdrw</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>ofdrw-gv</artifactId>
                    <groupId>org.ofdrw</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>xstream</artifactId>
                    <groupId>com.thoughtworks.xstream</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>common-dto-base</artifactId>
                    <groupId>com.juneyaoair</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jsqlparser</artifactId>
                    <groupId>com.github.jsqlparser</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mybatis</artifactId>
                    <groupId>org.mybatis</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.juneyaoair</groupId>
            <artifactId>cuss-client</artifactId>
            <version>${cuss.client.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.juneyaoair</groupId>
            <artifactId>common-dto-base</artifactId>
            <version>${cuss.client.version}</version>
        </dependency>
        <dependency>
            <groupId>com.juneyaoair.oneorder</groupId>
            <artifactId>component-aspect</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.juneyaoair.oneorder</groupId>
                    <artifactId>component-restresult</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

    <!-- 打包插件 -->
    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                            <goal>build-info</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.4</version>
                <executions>
                    <execution>
                        <id>report-aggregate</id>
                        <phase>verify</phase>
                        <goals>
                            <goal>report-aggregate</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>