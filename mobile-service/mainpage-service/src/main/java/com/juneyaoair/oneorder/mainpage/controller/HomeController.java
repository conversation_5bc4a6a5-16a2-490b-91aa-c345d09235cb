package com.juneyaoair.oneorder.mainpage.controller;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.pagehelper.PageInfo;
import com.juneyaoair.flightbasic.advertisement.AdvertisementDto;
import com.juneyaoair.flightbasic.advertisement.AdvertisementParam;
import com.juneyaoair.flightbasic.api.index.SaleOfficeReq;
import com.juneyaoair.flightbasic.api.index.SaleOfficeResp;
import com.juneyaoair.flightbasic.api.index.cobrandcreditcard.CoBrandCreditCardAdBO;
import com.juneyaoair.flightbasic.common.BaseResponseDTO;
import com.juneyaoair.flightbasic.common.BaseResultDTO;
import com.juneyaoair.flightbasic.commondto.RequestData;
import com.juneyaoair.flightbasic.feign.FlightBasicConsumerClient;
import com.juneyaoair.flightbasic.feign.FlightBasicProviderClient;
import com.juneyaoair.flightbasic.request.country.TCountryReqDTO;
import com.juneyaoair.flightbasic.request.notice.request.Message;
import com.juneyaoair.flightbasic.request.notice.request.NewsRequest;
import com.juneyaoair.flightbasic.request.notice.resposne.NewsDetailResponse;
import com.juneyaoair.flightbasic.request.notice.resposne.NewsResponse;
import com.juneyaoair.flightbasic.request.notice.resposne.NoticeResponse;
import com.juneyaoair.flightbasic.request.wechat.MessageRequestDTO;
import com.juneyaoair.flightbasic.response.country.TCountryDTO;
import com.juneyaoair.flightbasic.response.modular.ModularsDTO;
import com.juneyaoair.flightbasic.response.modular.ModularsParam;
import com.juneyaoair.flightbasic.theme.ThemeSwitchDTO;
import com.juneyaoair.flightbasic.theme.ThemeSwitchRequestDTO;
import com.juneyaoair.i18n.LocaleUtil;
import com.juneyaoair.mobile.exception.ArgumentCheckFailException;
import com.juneyaoair.mobile.exception.MultiLangServiceException;
import com.juneyaoair.mobile.exception.dto.ResponseData;
import com.juneyaoair.mobile.exception.errorcode.CommonErrorCode;
import com.juneyaoair.oneorder.annotation.ApiLog;
import com.juneyaoair.oneorder.api.basic.service.IBasicService;
import com.juneyaoair.oneorder.common.ServiceContext;
import com.juneyaoair.oneorder.common.dto.BizDto;
import com.juneyaoair.oneorder.common.dto.RequestDataDto;
import com.juneyaoair.oneorder.controller.BaseController;
import com.juneyaoair.oneorder.flight.dto.FlightNotice;
import com.juneyaoair.oneorder.flight.dto.FlightReminder;
import com.juneyaoair.oneorder.mainpage.config.MainPageConfig;
import com.juneyaoair.oneorder.mainpage.dto.AirportInfoDto;
import com.juneyaoair.oneorder.mainpage.dto.SpecialAirLine;
import com.juneyaoair.oneorder.mainpage.dto.homepage.*;
import com.juneyaoair.oneorder.mainpage.dto.homepage.querymessagelistbypage.NoticeResp;
import com.juneyaoair.oneorder.mainpage.service.IHomeService;
import com.juneyaoair.oneorder.mainpage.service.ISpecialAirLineService;
import com.juneyaoair.oneorder.mobile.utils.RedisUtil;
import com.juneyaoair.oneorder.page.PageResult;
import com.juneyaoair.oneorder.tools.utils.DateUtil;
import com.juneyaoair.oneorder.tools.utils.JacksonUtil;
import com.juneyaoair.oneorder.tools.utils.RedisKeyUtil;
import com.juneyaoair.mobile.exception.util.HoAirIpUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @description
 * @date 2023/5/25 10:43
 */

@Slf4j
@Api(value = "HomeController", tags = "页面常用服务")
@RequestMapping("/home")
@RestController
public class HomeController extends BaseController {

    @Resource
    private ISpecialAirLineService specialAirLineService;
    @Resource
    private IHomeService homeService;
    @Resource
    private IBasicService basicService;
    @Resource
    private MainPageConfig mainPageConfig;
    @Resource
    private FlightBasicConsumerClient flightBasicConsumerClient;
    @Resource
    private FlightBasicProviderClient flightBasicProviderClient;
    @Resource
    private RedisUtil redisUtil;
    @Autowired
    private LocaleUtil localeUtil;

    @Deprecated//换用queryMessageListByPage
    @ApiOperation(value = "获取公告列表", notes = "获取公告列表，此方法不支持分页，已废弃使用")
    @PostMapping("/queryMessageList")
    public ResponseData<List<NoticeResponse>> queryMessageList(@RequestBody @Validated RequestDataDto requestData, BindingResult bindingResult, HttpServletRequest request) {
        if (bindingResult.hasErrors()) {
            throw new ArgumentCheckFailException(bindingResult.getAllErrors().get(0).getDefaultMessage());
        }
        BizDto bizDto = initBizDto(request);
        //暂时固定为此类型
        MessageRequestDTO messageRequestDTO = new MessageRequestDTO();
        messageRequestDTO.setChannelCode(bizDto.getHeadChannelCode());
        messageRequestDTO.setMessageTypeCode("MSG_CD_NOTICE");
        BaseResponseDTO<List<NoticeResponse>> baseResponseDTO = flightBasicConsumerClient.fetchMessageList(messageRequestDTO);
        return ResponseData.suc(baseResponseDTO.getObjData());
    }

    @ApiOperation(value = "分页获取公告列表", notes = "分页获取公告列表")
    @PostMapping("/queryMessageListByPage")
    public ResponseData<PageResult<NoticeResp>> queryMessageListByPage(@RequestBody @Validated RequestDataDto<Message> requestData, BindingResult bindingResult, HttpServletRequest request) {
        if (bindingResult.hasErrors()) {
            throw new ArgumentCheckFailException(bindingResult.getAllErrors().get(0).getDefaultMessage());
        }
        Message msg = requestData.getData();
        msg.setLanguage(requestData.getLanguage().name());
        msg.setChannels(ServiceContext.getHead().channelNo);
        msg.setMessageEndTime(DateUtil.getCurrentDateStr());
        //暂时固定为此类型
        msg.setMessageType("MSG_CD_NOTICE");
        msg.setMessageStatus("Y");
        msg.setOrderBy("MESSAGE_STARTTIME DESC,MESSAGE_ENDTIME DESC,MESSAGE_UPDATETIME DESC");
        BaseResultDTO<PageInfo<NoticeResponse>> pageInfoBaseResultDTO = flightBasicProviderClient.queryMessageByPage(msg);
        if (pageInfoBaseResultDTO == null || pageInfoBaseResultDTO.getResult() == null) {
            throw MultiLangServiceException.fail("数据获取出错！");
        }
        PageInfo<NoticeResponse> respPage = pageInfoBaseResultDTO.getResult();
        PageResult<NoticeResp> ret = new PageResult<>();

        ret.setRows(Optional.of(respPage).map(p -> p.getList())
                .map(i -> i.stream().map(it -> NoticeResp.fromNoticeResponse(it)).collect(Collectors.toList()))
                .orElse(null));
        ret.setPageNum(respPage.getPageNum());
        ret.setPageSize(respPage.getPageSize());
        ret.setTotal(respPage.getTotal());
        return ResponseData.suc(ret);
    }

    @ApiOperation(value = "国内/国际地区特价航线", notes = "国内/国际地区特价航线")
    @PostMapping("/specialOfferSection")
    public ResponseData<List<SpecialAirLine>> querySpecialAirLine(@RequestBody @Validated RequestDataDto requestData, HttpServletRequest request) {
        String ip = HoAirIpUtil.getIpAddr(request);
        String channelCode = requestData.getChannelNo();
        BizDto bizDto = new BizDto(ip, channelCode);
        return ResponseData.suc(specialAirLineService.querySpecialAirlinePrice(bizDto, requestData.getCurrency(), requestData.getLanguage()));
    }

    @ApiOperation(value = "queryAppVersion", notes = "安卓版本下载地址")
    @PostMapping("/queryAppVersion")
    public ResponseData<String> queryAppVersion(@RequestBody @Validated RequestDataDto requestData) {
        return ResponseData.suc(mainPageConfig.getAppVersionUrl());
    }

    @ApiOperation(value = "获取轮播图配置", notes = "获取轮播图配置")
    @PostMapping("/queryRotationChart")
    public ResponseData<List<RotationChart>> queryRotationChart(@RequestBody @Validated RequestDataDto<RotationChartReq> requestData, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ArgumentCheckFailException(bindingResult.getAllErrors().get(0).getDefaultMessage());
        }
        return ResponseData.suc(homeService.queryRotationChart(requestData));
    }

    @ApiOperation(value = "获取数据版本信息", notes = "获取数据版本信息")
    @PostMapping("/queryDataVersion")
    public ResponseData queryDataVersion(@RequestBody @Validated RequestDataDto requestData, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ArgumentCheckFailException(bindingResult.getAllErrors().get(0).getDefaultMessage());
        }
        return ResponseData.suc(homeService.queryDataVersion(requestData));
    }

    @ApiOperation(value = "航班公告信息提醒", notes = "航班公告信息提醒")
    @PostMapping("/queryFlightNotice")
    public ResponseData<List<FlightReminder>> queryFlightNotice(@RequestBody @Validated RequestDataDto<FlightNotice> requestData, BindingResult bindingResult) {
        if (ObjectUtils.isEmpty(requestData.getData())) {
            throw new ArgumentCheckFailException("业务参数不可为空");
        }
        if (bindingResult.hasErrors()) {
            throw new ArgumentCheckFailException(bindingResult.getAllErrors().get(0).getDefaultMessage());
        }
        return ResponseData.suc(homeService.queryFlightNotice(requestData));
    }

    @ApiOperation(value = "getPackageAd", notes = "获取旅游度假精选")
    @PostMapping("/getPackageAd")
    public ResponseData<String> getPackageAd(@RequestBody @Validated RequestDataDto requestData, BindingResult bindingResult) {
        checkParam(requestData, bindingResult);
        return ResponseData.suc(homeService.getPackageAd());
    }

    @ApiOperation(value = "getAdvertise", notes = "获取公司运营报告，责任报告")
    @PostMapping("/getAdvertise")
    public ResponseData<PageInfo<AdvertisementDto>> queryAdvertisementListByPage(@RequestBody @Validated RequestDataDto<AdvertisementParam> requestData, BindingResult bindingResult, HttpServletRequest request) {
        checkParam(requestData, bindingResult);
        BizDto bizDto = new BizDto();
        initBizDto(bizDto, request);
        return ResponseData.suc(homeService.queryAdvertisementListByPage(bizDto, requestData.getData()));
    }

    @ApiOperation(value = "查询机场服务帮助信息", notes = "机场服务帮助")
    @PostMapping("/queryAllAirportInfo")
    public ResponseData<List<AirportInfoDto>> queryAllAirportInfo(@RequestBody @Validated RequestDataDto requestData, BindingResult bindingResult, HttpServletRequest request) {
        if (bindingResult.hasErrors()) {
            throw new ArgumentCheckFailException(bindingResult.getAllErrors().get(0).getDefaultMessage());
        }
        BizDto bizDto = initBizDto(request);
        return ResponseData.suc(homeService.queryAllAirportInfo(bizDto));
    }

    @ApiOperation(value = "查询国家信息", notes = "查询国家信息")
    @PostMapping("/queryCountries")
    public ResponseData<List<TCountryDTO>> queryCountries(@RequestBody @Validated RequestDataDto<TCountryReqDTO> requestData, BindingResult bindingResult, HttpServletRequest request) {
        if (bindingResult.hasErrors()) {
            throw new ArgumentCheckFailException(bindingResult.getAllErrors().get(0).getDefaultMessage());
        }
        BizDto bizDto = initBizDto(request);
        return ResponseData.suc(basicService.queryCountries(bizDto.getIp(), requestData.getChannelNo(), requestData.getData()));
    }

    @ApiOperation(value = "获取营业部info", notes = "获取营业部info ")
    @PostMapping("/getSaleOffice")
    public ResponseData<SaleOfficeResp> getSaleOffice(@RequestBody @Validated RequestDataDto<SaleOfficeReq> requestData, BindingResult bindingResult, HttpServletRequest request) {
        if (bindingResult.hasErrors()) {
            throw new ArgumentCheckFailException(bindingResult.getAllErrors().get(0).getDefaultMessage());
        }
        return ResponseData.suc(basicService.getSaleOffice(requestData.getData(), requestData.getLanguage()));
    }

    @ApiOperation(value = "分页获取吉祥新闻列表", notes = "分页获取吉祥新闻列表")
    @PostMapping("/toCatchNewsByPage")
    public ResponseData<PageResult<NewsResponse>> toCatchNewsByPage(@RequestBody @Validated RequestDataDto<NewsRequest> requestData, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ArgumentCheckFailException(bindingResult.getAllErrors().get(0).getDefaultMessage());
        }
        NewsRequest newsRequest = requestData.getData();
        newsRequest.setLoginCd(StringUtils.isEmpty(requestData.getFfpId()) ? "N" : "");
        newsRequest.setChannelCode(StringUtils.isNotEmpty(requestData.getChannelNo()) ? requestData.getChannelNo() : "B2C");
        BaseResultDTO<PageInfo<NewsResponse>> catchJXNews = flightBasicProviderClient.toCatchJXNews(newsRequest);
        if (catchJXNews == null) {
            throw MultiLangServiceException.fail("数据获取出错！");
        }
        if (catchJXNews.getResult() == null) {
            throw MultiLangServiceException.fail("数据获取为空！");
        }
        PageInfo<NewsResponse> respPage = catchJXNews.getResult();
        PageResult<NewsResponse> ret = new PageResult<>();

        ret.setRows(respPage.getList());
        ret.setPageNum(respPage.getPageNum());
        ret.setPageSize(respPage.getPageSize());
        ret.setTotal(respPage.getTotal());
        return ResponseData.suc(ret);
    }

    @ApiOperation(value = "获取吉祥新闻详情", notes = "获取吉祥新闻详情")
    @PostMapping("/toCatchNewsDetail")
    public ResponseData<NewsDetailResponse> toCatchNewsDetail(@RequestBody @Validated RequestDataDto<NewsRequest> requestData, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ArgumentCheckFailException(bindingResult.getAllErrors().get(0).getDefaultMessage());
        }
        NewsRequest newsRequest = requestData.getData();
        if (null == newsRequest || newsRequest.getNewsId() == 0) {
            throw MultiLangServiceException.fail("消息唯一标识不可为空！");
        }
        newsRequest.setLoginCd(StringUtils.isEmpty(requestData.getFfpId()) ? "N" : "");
        newsRequest.setChannelCode(StringUtils.isNotEmpty(requestData.getChannelNo()) ? requestData.getChannelNo() : "B2C");
        BaseResultDTO<NewsDetailResponse> catchJXNewsDetail = flightBasicProviderClient.toCatchJXNewsDetail(newsRequest);
        if (catchJXNewsDetail == null) {
            throw MultiLangServiceException.fail("数据获取出错！");
        }
        if (catchJXNewsDetail.getResult() == null) {
            throw MultiLangServiceException.fail("数据获取为空！");
        }
        NewsDetailResponse result = catchJXNewsDetail.getResult();
        return ResponseData.suc(result);
    }

    @ApiOperation(value = "获取微信二维码info", notes = "获取微信二维码info")
    @PostMapping("/getWechatUnlimitedQRCode")
    @ApiLog
    public ResponseData<GetWechatQRCodeBffResp> getWechatUnlimitedQRCode(@RequestBody @Validated RequestDataDto<GetWechatQRCodeBffReq> req) {
        return ResponseData.suc(homeService.getSuccessResponseData(req));
    }

    @ApiOperation(value = "获取微信二维码原scene", notes = "获取微信二维码原scene")
    @PostMapping("/getSceneOrig")
    @ApiLog
    public ResponseData<SceneInfoResp> getSceneOrig(@RequestBody @Validated RequestDataDto<SceneInfoReq> req) {
        String wxQRCodeKey = RedisKeyUtil.getWXQRCode(req.getData().sceneSign);
        Object info = redisUtil.get(wxQRCodeKey);
        if (info == null) {
            throw MultiLangServiceException.fail("信息已过期！");
        }
        WechatQRCodeInfoPO wechatQRCodePO = JacksonUtil.objectToDto(info, new TypeReference<WechatQRCodeInfoPO>() {
        });

        if (wechatQRCodePO == null || StrUtil.isBlank(wechatQRCodePO.sceneOrig)) {
            throw MultiLangServiceException.fail(CommonErrorCode.BUSINESS_ERROR.getMessage());
        }

        SceneInfoResp infoResp = new SceneInfoResp();
        infoResp.scene = wechatQRCodePO.sceneOrig;
        return ResponseData.suc(infoResp);
    }

    @ApiOperation(value = "联名信用卡广告信息", notes = "联名信用卡广告信息")
    @PostMapping("/creditCardAd")
    public ResponseData<List<CoBrandCreditCardAdBO>> creditCardAdDetail(@RequestBody @Validated RequestDataDto requestData, HttpServletRequest request) {
        BizDto bizDto = initBizDto(request);
        return ResponseData.suc(homeService.getCreditCardAd(bizDto));
    }

    @ApiOperation(value = "i18n测试", notes = "i18n测试")
    @PostMapping("/testi18n")
    public ResponseData testi18n(@RequestBody @Validated RequestDataDto requestDataDto, HttpServletRequest httpServletRequest) {
        BizDto bizDto = initBizDto(httpServletRequest);
        if ("B2C".equals(requestDataDto.getChannelNo())) {
            throw new ArgumentCheckFailException("i18n测试");
        }
        return ResponseData.suc(localeUtil.getTips("result"));
    }

    @ApiOperation(value = "获取主题切换状态", notes = "获取是否开启黑白主题")
    @PostMapping("/themeSwitch")
    public ResponseData<ThemeSwitchResp> themeSwitch(@RequestBody @Validated RequestDataDto requestData) {

        RequestData clientRequest = new RequestData();
        ThemeSwitchRequestDTO requestDTO = new ThemeSwitchRequestDTO();
        requestDTO.setChannels(Arrays.asList(requestData.getChannelNo()));
        clientRequest.setData(requestDTO);

        com.juneyaoair.flightbasic.commondto.ResponseData<ThemeSwitchDTO> themeResult = flightBasicProviderClient.getThemeSwitch(clientRequest);
        if (themeResult == null) {
            throw MultiLangServiceException.fail("获取主题配置失败");
        }
        ThemeSwitchResp resp = new ThemeSwitchResp();
        resp.setEnableGrayTheme(themeResult.getData().getChannelStatus().get(requestData.getChannelNo()));
        return ResponseData.suc(resp);
    }

    @ApiOperation(value = "查询积分商城商品", notes = "查询积分商城商品列表")
    @PostMapping(value = "/queryMallProducts", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseData<MallProductResponse> queryMallProducts(@RequestBody @Validated RequestDataDto<MallProductRequest> requestData,HttpServletRequest httpServletRequest) {
        MallProductRequest mallRequest = requestData.getData();
        if (mallRequest == null) {
            mallRequest = new MallProductRequest();
        }
        BizDto bizDto = initBizDto(httpServletRequest);
        return ResponseData.suc(homeService.queryMallProducts(mallRequest,bizDto));
    }

    @ApiOperation(value = "获取服务模块-子服务清单", notes = "获取服务模块-子服务清单")
    @PostMapping("/getModular")
    public ResponseData<ModularsDTO> getModular(@RequestBody @Validated RequestDataDto<ModularsParam> requestData) {
        RequestData<ModularsParam> flightBasicRequest = new RequestData<>();
        flightBasicRequest.setChannelNo(requestData.getChannelNo());
        flightBasicRequest.setFfpId(requestData.getFfpId());
        flightBasicRequest.setFfpNo(requestData.getFfpNo());
        flightBasicRequest.setOriginIp(requestData.getOriginIp());
        flightBasicRequest.setLanguage(requestData.getLanguage().name());
        flightBasicRequest.setData(requestData.getData());
        com.juneyaoair.flightbasic.commondto.ResponseData<ModularsDTO> responseData = flightBasicProviderClient.getModular(flightBasicRequest);
        if (responseData == null) {
            throw MultiLangServiceException.fail("查询失败");
        }
        return ResponseData.suc(responseData.getData());
    }

}
