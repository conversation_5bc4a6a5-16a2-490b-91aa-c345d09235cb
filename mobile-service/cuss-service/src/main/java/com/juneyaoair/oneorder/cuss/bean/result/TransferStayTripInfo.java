package com.juneyaoair.oneorder.cuss.bean.result;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 中转住宿行程信息
 * <AUTHOR>
 */
@Data
public class TransferStayTripInfo {

    @ApiModelProperty(value = "票号")
    private String ticketNo;

    @ApiModelProperty(value = "姓名")
    private String passengerName;

    @ApiModelProperty(value = "总时长 单位：分钟")
    private Long totalDuration;

    @ApiModelProperty(value = "中转行程航班信息")
    private List<TransferFlightInfo> transferFlightList;

}
