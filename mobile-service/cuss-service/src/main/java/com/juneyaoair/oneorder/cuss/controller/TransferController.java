package com.juneyaoair.oneorder.cuss.controller;

import com.google.common.collect.Sets;
import com.juneyaoair.flightbasic.appenum.MemberDetailRequestItemsEnum;
import com.juneyaoair.mobile.exception.MultiLangServiceException;
import com.juneyaoair.mobile.exception.errorcode.CommonErrorCode;
import com.juneyaoair.oneorder.api.crm.service.IMemberService;
import com.juneyaoair.oneorder.common.dto.RequestDataDto;
import com.juneyaoair.oneorder.crm.dto.common.MemberBasicInfoSoaModel;
import com.juneyaoair.oneorder.crm.dto.common.MemberCertificateSoaModelV2;
import com.juneyaoair.oneorder.crm.dto.response.PtMemberDetail;
import com.juneyaoair.oneorder.cuss.bean.result.TransferStayTripResult;
import com.juneyaoair.oneorder.order.constant.CertificateTypeEnum;
import com.juneyaoair.oneorder.restresult.response.ResponseData;
import com.juneyaoair.oneorder.cuss.service.TransferService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * 中转行程
 * <AUTHOR>
 */
@Slf4j
@Api(value = "TransferController", tags = "中转行程")
@RestController
public class TransferController {

    @Autowired
    private IMemberService memberService;
    @Autowired
    private TransferService transferService;

    @ApiOperation(value = "获取旅客中转住宿行程信息")
    @PostMapping("/transfer/getTransferStayTrip")
    public ResponseData<TransferStayTripResult> getTransferStayTrip(@RequestBody @Validated RequestDataDto requestData) {
        // 查询会员信息
        PtMemberDetail ptMemberDetail = memberService.memberDetail(requestData.getChannelNo(), requestData.getFfpNo(), new String[]{MemberDetailRequestItemsEnum.BASICINFO.eName, MemberDetailRequestItemsEnum.CERTIFICATEINFO.eName});
        List<MemberCertificateSoaModelV2> certificateInfoList = ptMemberDetail.getCertificateInfo();
        // 检查会员是否实名
        if (CollectionUtils.isEmpty(certificateInfoList)) {
            throw new MultiLangServiceException(CommonErrorCode.NO_REAL_NAME, CommonErrorCode.NO_REAL_NAME.getMessage());
        }
        Optional<MemberCertificateSoaModelV2> certInfo = certificateInfoList.stream().filter(MemberCertificateSoaModelV2::isVerify).findFirst();
        if (!certInfo.isPresent()) {
            throw new MultiLangServiceException(CommonErrorCode.NO_REAL_NAME, CommonErrorCode.NO_REAL_NAME.getMessage());
        }
        MemberBasicInfoSoaModel basicInfo = ptMemberDetail.getBasicInfo();
        Set<String> certificateNoSet = Sets.newHashSet();
        String travellerName = null;
        for (MemberCertificateSoaModelV2 memberCertificateSoaModel : certificateInfoList) {
            CertificateTypeEnum certificateTypeEnum = CertificateTypeEnum.checkType(memberCertificateSoaModel.getCertificateType());
            // 身份证使用中文姓名，其他证件使用英文姓名
            if (CertificateTypeEnum.PASSPORT.equals(certificateTypeEnum)) {
                travellerName = basicInfo.getELastName() + "/" + basicInfo.getEFirstName();
                certificateNoSet.add(memberCertificateSoaModel.getCertificateNumber());
                break;
            }
        }
        TransferStayTripResult transferStayTrip = transferService.getTransferStayTrip(requestData.getChannelNo(), certificateNoSet, travellerName);
        return ResponseData.success(transferStayTrip);
    }

}
