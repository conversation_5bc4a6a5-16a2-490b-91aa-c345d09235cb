package com.juneyaoair.oneorder.cuss.controller;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.juneyaoair.common.dto.base.BaseRequestDTO;
import com.juneyaoair.common.dto.base.BaseResultDTO;
import com.juneyaoair.cuss.dto.booking.response.seat.FlightSeatStatusResult;
import com.juneyaoair.cuss.enums.ENUM_FLIGHT_SEAT_STATUS;
import com.juneyaoair.cuss.fegin.CussBookingClient;
import com.juneyaoair.cuss.param.trip.TravellerTripLimit;
import com.juneyaoair.cuss.param.trip.TravellerTripParam;
import com.juneyaoair.cuss.request.FlightSeatStatusParam;
import com.juneyaoair.cuss.request.travel.FlightBoardQueryBeanDTO;
import com.juneyaoair.cuss.response.seat.SeatOrderInfo;
import com.juneyaoair.cuss.response.travel.BoardInfoResultDTO;
import com.juneyaoair.cuss.response.trip.TravellerTicketInfo;
import com.juneyaoair.cuss.response.trip.TravellerTripInfo;
import com.juneyaoair.cuss.service.CussBookingService;
import com.juneyaoair.cuss.service.CussCustomerService;
import com.juneyaoair.cuss.util.CabinUtils;
import com.juneyaoair.cuss.util.CussClientUtil;
import com.juneyaoair.cuss.util.NameUtils;
import com.juneyaoair.mobile.exception.MultiLangServiceException;
import com.juneyaoair.mobile.exception.errorcode.CommonErrorCode;
import com.juneyaoair.oneorder.api.common.CommonService;
import com.juneyaoair.oneorder.api.crm.constant.TripTypeEnum;
import com.juneyaoair.oneorder.api.crm.service.IMemberService;
import com.juneyaoair.oneorder.api.geetest.sdk.enums.SceneEnum;
import com.juneyaoair.oneorder.api.geetest.service.IGeetestService;
import com.juneyaoair.oneorder.common.dto.CheckDayLicense;
import com.juneyaoair.oneorder.common.dto.RequestDataDto;
import com.juneyaoair.oneorder.common.dto.enums.ChannelCodeEnum;
import com.juneyaoair.oneorder.common.dto.enums.CheckLicenseFuncEnum;
import com.juneyaoair.oneorder.common.dto.enums.EticketStatusEnum;
import com.juneyaoair.oneorder.common.service.TongDunService;
import com.juneyaoair.oneorder.common.util.TraceUtil;
import com.juneyaoair.oneorder.controller.BaseController;
import com.juneyaoair.oneorder.cuss.bean.param.FlightTravellerTripParam;
import com.juneyaoair.oneorder.cuss.bean.param.QueryTravellerTrip;
import com.juneyaoair.oneorder.cuss.bean.result.BffTravellerTripInfo;
import com.juneyaoair.oneorder.cuss.bean.result.TravellerTripResult;
import com.juneyaoair.oneorder.cuss.config.CussApolloConfig;
import com.juneyaoair.oneorder.cuss.mapstruct.TravellerTripInfoMapper;
import com.juneyaoair.oneorder.cuss.service.TravellerTripService;
import com.juneyaoair.oneorder.cuss.utils.BaseRequestUtil;
import com.juneyaoair.oneorder.order.constant.AirCompanyEnum;
import com.juneyaoair.oneorder.restresult.response.ResponseData;
import com.juneyaoair.oneorder.util.LoginCheckUtil;
import com.juneyaoair.oneorder.util.MetricLogUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @Description 旅客行程接口
 * @created 2023/11/15 10:28
 */
@Slf4j
@Api(value = "旅客行程接口")
@RestController
public class TravellerTripController extends BaseController {

    @Autowired
    private CussApolloConfig cussApolloConfig;

    @Autowired
    private CussBookingClient cussBookingClient;

    @Autowired
    private CommonService commonService;
    @Autowired
    private CussBookingService cussBookingService;
    @Autowired
    private CussCustomerService cussCustomerService;
    @Autowired
    private TravellerTripService travellerTripService;
    @Autowired
    private IMemberService memberService;
    @Autowired
    private IGeetestService geetestService;
    @Autowired
    private TongDunService tongDunService;

    @ApiOperation(value = "获取旅客行程信息")
    @PostMapping("/travellerTrip/getTravellerTrip")
    public ResponseData<TravellerTripResult> getTravellerTrip(@RequestBody @Validated RequestDataDto<QueryTravellerTrip> queryTravellerTripBase, HttpServletRequest request) {
        // 检查接口是否访问合规
        LoginCheckUtil.checkLogin(queryTravellerTripBase, ChannelCodeEnum.G_B2C);
        if (!ChannelCodeEnum.HOCAR.getChannelCode().equals(queryTravellerTripBase.getChannelNo())) {
            // 同盾校验
            tongDunService.checkInSeatControl(queryTravellerTripBase, initBizDto(request));
        }
        QueryTravellerTrip queryTravellerTrip = queryTravellerTripBase.getData();
        String type = queryTravellerTrip.getType();
        // 日志
        JSONObject tags = new JSONObject();
        tags.put("Group", type);
        MetricLogUtils.saveMetricLog("值机选座行程-客票提取", tags, queryTravellerTripBase);

        TravellerTripParam travellerTripParam = new TravellerTripParam();
        // 排除BUS行程
        TravellerTripLimit travellerTripLimit = new TravellerTripLimit();
        travellerTripLimit.setExcludePlaneTyp(Sets.newHashSet("BUS"));
        travellerTripParam.setTravellerTripLimit(travellerTripLimit);
        switch (type) {
            case "member":
                // 未登录 报错
                if (!queryTravellerTripBase.isLoginFlag()) {
                    throw new MultiLangServiceException(CommonErrorCode.NOT_LOGIN);
                }
                // 实名会员行程改为基于会员卡号查询
                boolean realState = memberService.realState(queryTravellerTripBase.getFfpNo());
                if (!realState) {
                    throw new MultiLangServiceException(CommonErrorCode.NO_REAL_NAME, CommonErrorCode.NO_REAL_NAME.getMessage());
                }
                // 基于会员卡号查询
                travellerTripParam.setFfpCardNo(queryTravellerTripBase.getFfpNo());
                break;
            case "number":
                // 查询错误次数限制
                CheckDayLicense ipCheckDayLicense = new CheckDayLicense(queryTravellerTripBase.getOriginIp(), CheckLicenseFuncEnum.TRAVELLER_TRIP_IP, "行程查询失败，达到单日IP查询上限");
                CheckDayLicense noDataCheckDayLicenseIp = new CheckDayLicense(queryTravellerTripBase.getOriginIp(), CheckLicenseFuncEnum.TRAVELLER_TRIP_NO_DATA_IP, "行程查询失败，达到单日失败上限");
                CheckDayLicense ffpCheckDayLicense = null;
                CheckDayLicense noDataCheckDayLicenseFfp = null;
                if (queryTravellerTripBase.isLoginFlag()) {
                    ffpCheckDayLicense = new CheckDayLicense(queryTravellerTripBase.getFfpNo(), CheckLicenseFuncEnum.TRAVELLER_TRIP_FFP, "行程查询失败，达到单日会员查询上限");
                    noDataCheckDayLicenseFfp = new CheckDayLicense(queryTravellerTripBase.getFfpNo(), CheckLicenseFuncEnum.TRAVELLER_TRIP_NO_DATA_FFP, "行程查询失败，达到单日失败上限");
                }
                // ip、会员增加计数
                commonService.checkDayLicense(true, ipCheckDayLicense, ffpCheckDayLicense);
                // 无数据 ip、会员不增加计数
                commonService.checkDayLicense(false, noDataCheckDayLicenseIp, noDataCheckDayLicenseFfp);
                // 是否需要校验极验 车机不需要
                boolean checkGeetest = !ChannelCodeEnum.HOCAR.getChannelCode().equals(queryTravellerTripBase.getChannelNo());
                // 极验验证
                if (checkGeetest) {
                    geetestService.validate(SceneEnum.CUSS_TRAVELLER_TRIP, queryTravellerTripBase);
                }
                if (StringUtils.isBlank(queryTravellerTrip.getTravellerName())) {
                    throw new MultiLangServiceException(CommonErrorCode.REQUEST_VALIDATION_FAILED, "旅客姓名不能为空");
                }
                if (StringUtils.isBlank(queryTravellerTrip.getTravellerNumber())) {
                    throw new MultiLangServiceException(CommonErrorCode.REQUEST_VALIDATION_FAILED, "证件号/票号不能为空");
                }
                if (StringUtils.isBlank(queryTravellerTrip.getFlightNo())) {
                    throw new MultiLangServiceException(CommonErrorCode.REQUEST_VALIDATION_FAILED, "航班号不能为空");
                }
                // 基于证件姓名查询
                travellerTripParam.setTravellerNumber(queryTravellerTrip.getTravellerNumber().replaceAll("-", "").toUpperCase());
                travellerTripParam.setTravellerName(queryTravellerTrip.getTravellerName());
                break;
            default:
                throw new MultiLangServiceException("查询类型不正确！");
        }
        try {
            TravellerTripResult travellerTripResult = travellerTripService.getTravellerTripResult(queryTravellerTripBase, queryTravellerTripBase.getData(), travellerTripParam);
            return ResponseData.success(travellerTripResult);
        } catch (MultiLangServiceException mse) {
            // 无行程保存
            if (CommonErrorCode.SEAR_TRAVELLER_NO_TRIP.equals(mse.getError())) {
                // 查询错误次数限制
                CheckDayLicense noDataCheckDayLicenseIp = new CheckDayLicense(queryTravellerTripBase.getOriginIp(), CheckLicenseFuncEnum.TRAVELLER_TRIP_NO_DATA_IP, "行程查询失败，达到单日失败上限");
                CheckDayLicense noDataCheckDayLicenseFfp = null;
                if (queryTravellerTripBase.isLoginFlag()) {
                    noDataCheckDayLicenseFfp = new CheckDayLicense(queryTravellerTripBase.getFfpNo(), CheckLicenseFuncEnum.TRAVELLER_TRIP_NO_DATA_FFP, "行程查询失败，达到单日失败上限");
                }
                commonService.addDayLicense(noDataCheckDayLicenseIp, noDataCheckDayLicenseFfp);
            }
            throw mse;
        }
    }

    @ApiOperation(value = "查询同一航班多个旅客行程信息")
    @PostMapping("/travellerTrip/getTravellerTripList")
    public ResponseData<TravellerTripInfo> getTravellerTripList(@RequestBody @Validated RequestDataDto<FlightTravellerTripParam> flightTravellerTripParamBase, HttpServletRequest request) {
        // 同盾校验
        tongDunService.checkInSeatControl(flightTravellerTripParamBase, initBizDto(request));
        FlightTravellerTripParam flightTravellerTripParam = flightTravellerTripParamBase.getData();
        Map<String, String> ticketMap = Maps.newHashMap();
        flightTravellerTripParam.getTravellerSet().forEach(travellerInfo -> ticketMap.put(travellerInfo.getTicketNo().replaceAll("-", ""), travellerInfo.getTravellerName()));
        if (ticketMap.size() != flightTravellerTripParam.getTravellerSet().size()) {
            throw new MultiLangServiceException(CommonErrorCode.REQUEST_VALIDATION_FAILED, "存在重复票号");
        }
        // 检查航班是否支持值机选座
        FlightSeatStatusResult flightSeatStatus = cussBookingService.getFlightSeatStatus(BaseRequestUtil.createRequest(flightTravellerTripParamBase));
        if (ENUM_FLIGHT_SEAT_STATUS.NONE.equals(flightSeatStatus.getFlightSeatStatus())) {
            throw new MultiLangServiceException(CommonErrorCode.REQUEST_VALIDATION_FAILED, "当前航班不支持线上值机选座或已过值机选座截止办理时间");
        }
        MetricLogUtils.saveMetricLog("订单页值机选座-客票提取", flightTravellerTripParamBase);
        String traceId = TraceUtil.getTraceId();
        List<TravellerTripInfo> finalTravellerTripInfoList = Collections.synchronizedList(Lists.newArrayList());
        flightTravellerTripParam.getTravellerSet().parallelStream().forEach(travellerInfo -> {
            TravellerTripParam travellerTripParam = new TravellerTripParam();
            String uuid = UUID.randomUUID().toString();
            try {
                travellerTripParam.setTravellerNumber(travellerInfo.getTicketNo());
                travellerTripParam.setTravellerName(travellerInfo.getTravellerName());
                BaseRequestDTO<TravellerTripParam> travellerTripParamBase = BaseRequestUtil.createRequest(flightTravellerTripParamBase, travellerTripParam);
                log.info("调用CUSS行程接口开始，traceId:{} 请求ID：{} 请求参数：{}", traceId, uuid, JSON.toJSONString(travellerTripParamBase));
                BaseResultDTO<com.juneyaoair.cuss.response.trip.TravellerTripResult> baseResult = cussBookingClient.getTravellerTrip(travellerTripParamBase, uuid);
                log.info("调用CUSS行程接口结束，请求ID：{} 返回结果：{}", uuid, JSON.toJSONString(baseResult));
                com.juneyaoair.cuss.response.trip.TravellerTripResult travellerTripResult = CussClientUtil.getResult(baseResult);
                if (null == travellerTripResult || CollectionUtils.isEmpty(travellerTripResult.getTravellerTripInfoList())) {
                    log.info("未获取到旅客行程信息，traceId:{} 请求ID：{} 请求参数：{}", traceId, uuid, JSON.toJSONString(travellerTripParam));
                    return;
                }
                finalTravellerTripInfoList.addAll(travellerTripResult.getTravellerTripInfoList());
            } catch (Exception e) {
                log.error("查询旅客行程信息失败，traceId:{} 请求ID：{} 请求参数：{} 异常信息：", traceId, uuid, JSON.toJSONString(travellerTripParam), e);
            }
        });

        TravellerTripInfo travellerTripInfoResult = null;
        // 待支付选座
        List<TravellerTicketInfo> unPaySeatList = Lists.newArrayList();
        // 可选座且未选座
        List<TravellerTicketInfo> canSeatList = Lists.newArrayList();
        // 已选座
        List<TravellerTicketInfo> seatList = Lists.newArrayList();
        // 不可选座
        List<TravellerTicketInfo> unSeatList = Lists.newArrayList();
        List<TravellerTripInfo> travellerTripInfoList = Lists.newArrayList(finalTravellerTripInfoList);
        for (TravellerTripInfo travellerTripInfo : travellerTripInfoList) {
            // 航班信息是否匹配
            boolean match = (flightTravellerTripParam.getFlightNo().equalsIgnoreCase(travellerTripInfo.getMarketingFlightNo()) ||
                                flightTravellerTripParam.getFlightNo().equalsIgnoreCase(travellerTripInfo.getFlightNo()) ||
                                flightTravellerTripParam.getFlightNo().equalsIgnoreCase(travellerTripInfo.getOperationFlightNo())) &&
                            flightTravellerTripParam.getFlightDate().equalsIgnoreCase(travellerTripInfo.getFlightDate()) &&
                            flightTravellerTripParam.getDepAirportCode().equalsIgnoreCase(travellerTripInfo.getDepAirportCode()) &&
                            flightTravellerTripParam.getArrAirportCode().equalsIgnoreCase(travellerTripInfo.getArrAirportCode());
            if (!match) {
                continue;
            }
            if (null == travellerTripInfoResult) {
                travellerTripInfoResult = TravellerTripInfoMapper.MAPPER.toTravellerTripInfo(travellerTripInfo);
            }
            for (TravellerTicketInfo travellerTicketInfo : travellerTripInfo.getTravellerList()) {
                String paramName = ticketMap.get(travellerTicketInfo.getTicketNo().replaceAll("-", ""));
                // 入参存在姓名校验姓名
                if (!NameUtils.patternName(paramName, travellerTicketInfo.getTravellerName())) {
                    log.info("客票：{} 姓名不匹配，入参姓名：{} 客票姓名：{}", travellerTicketInfo.getTicketNo(), paramName, travellerTicketInfo.getTravellerName());
                    continue;
                }
                // 存在提示信息 放入不可选座清单
                if (StringUtils.isNotBlank(travellerTicketInfo.getTip())) {
                    unSeatList.add(travellerTicketInfo);
                    continue;
                }
                // 存在座位号
                if (StringUtils.isNotBlank(travellerTicketInfo.getSeatNo())) {
                    SeatOrderInfo seatOrderInfo = travellerTicketInfo.getSeatOrderInfo();
                    // 不存在订单 放入已选座清单
                    if (null == seatOrderInfo) {
                        seatList.add(travellerTicketInfo);
                    }
                    // 待支付订单
                    else if ("UN_PAY".equals(seatOrderInfo.getSeatState())) {
                        unPaySeatList.add(travellerTicketInfo);
                    } else {
                        seatList.add(travellerTicketInfo);
                    }
                    continue;
                }
                // 航班不支持选座 放入不可选座清单
                if (ENUM_FLIGHT_SEAT_STATUS.NONE.equals(travellerTripInfo.getFlightSeatStatus())) {
                    unSeatList.add(travellerTicketInfo);
                } else {
                    canSeatList.add(travellerTicketInfo);
                }
            }
        }
        List<TravellerTicketInfo> travellerTicketInfoList = Lists.newArrayList();
        travellerTicketInfoList.addAll(unPaySeatList);
        travellerTicketInfoList.addAll(canSeatList);
        travellerTicketInfoList.addAll(seatList);
        travellerTicketInfoList.addAll(unSeatList);
        if (CollectionUtils.isEmpty(travellerTicketInfoList)) {
            throw new MultiLangServiceException("无有效行程信息");
        }
        travellerTripInfoResult.setTravellerList(travellerTicketInfoList);
        return ResponseData.success(travellerTripInfoResult);
    }

    @ApiOperation(value = "获取旅客升舱行程信息")
    @PostMapping("/travellerTrip/getUpgTravellerTrip")
    public ResponseData<TravellerTripResult> getUpgTravellerTrip(@RequestBody @Validated RequestDataDto<Object> requestData) {
        // 日志
        JSONObject tags = new JSONObject();
        tags.put("Group", "member");
        MetricLogUtils.saveMetricLog("升舱行程-客票提取", tags, requestData);
        // 会员账号未实名返回空行程
        boolean realState = memberService.realState(requestData.getFfpNo());
        if (!realState) {
            return ResponseData.success();
        }
        // 基于会员卡号查询旅客行程信息
        List<TravellerTripInfo> travellerTripList = travellerTripService.getTravellerTrip(requestData, null);
        if (CollectionUtils.isEmpty(travellerTripList)) {
            return ResponseData.success();
        }
        List<TravellerTripInfo> resultList = Lists.newArrayList();
        for (TravellerTripInfo travellerTripInfo : travellerTripList) {
            // 非上海出发航班
            if (!"SHA".equalsIgnoreCase(travellerTripInfo.getDepCityCode())) {
                continue;
            }
            // 非国内航线
            if (!TripTypeEnum.TRIP_TYPE_D.getCode().equalsIgnoreCase(travellerTripInfo.getInterFlag())) {
                continue;
            }
            // BUS行程
            if ("BUS".equalsIgnoreCase(travellerTripInfo.getPlaneType())) {
                continue;
            }
            // 非吉祥承运航班
            if (!travellerTripInfo.getOperationFlightNo().startsWith(AirCompanyEnum.HO.getAirCompanyCode())) {
                continue;
            }
            List<TravellerTicketInfo> travellerList = Lists.newArrayList();
            for (TravellerTicketInfo travellerTicketInfo : travellerTripInfo.getTravellerList()) {
                // 非018票本
                if (!travellerTicketInfo.getTicketNo().startsWith("018")) {
                    continue;
                }
                // 存在特服
                if (CollectionUtils.isNotEmpty(travellerTicketInfo.getSpecialPassenger())) {
                    continue;
                }
                // 团队客票（E舱）、中转联程客票（G舱）
                if ("E".equalsIgnoreCase(travellerTicketInfo.getCabin()) || "G".equalsIgnoreCase(travellerTicketInfo.getCabin())) {
                    continue;
                }
                // 公务舱
                if (CabinUtils.CABIN_J.equals(CabinUtils.getHighestClassCabin(travellerTicketInfo.getCabin()))) {
                    continue;
                }
                // 非值机状态
                if (!EticketStatusEnum.CHECKED_IN.getCode().equals(travellerTicketInfo.getTicketStatus())) {
                    continue;
                }
                travellerList.add(travellerTicketInfo);
            }
            if (CollectionUtils.isNotEmpty(travellerList)) {
                TravellerTripInfo resultTravellerTripInfo = new TravellerTripInfo();
                BeanUtils.copyProperties(travellerTripInfo, resultTravellerTripInfo, "travellerList");
                resultTravellerTripInfo.setTravellerList(travellerList);
                resultList.add(resultTravellerTripInfo);
            }
        }
        TravellerTripResult travellerTripResult = new TravellerTripResult();
        travellerTripResult.setTravellerTripInfoList(resultList);
        return ResponseData.success(travellerTripResult);
    }

    @ApiOperation(value = "获取旅客最近的一次行程信息")
    @PostMapping("/travellerTrip/getFirstTravellerTrip")
    public ResponseData<BffTravellerTripInfo> getFirstTravellerTrip(@RequestBody @Validated RequestDataDto<Object> requestData) {
        // 是否启用首页行程
        if (!cussApolloConfig.isFirstTravellerTripFlag()) {
            return ResponseData.success(null);
        }
        // 会员账号未实名返回空行程
        boolean realState = memberService.realState(requestData.getFfpNo());
        if (!realState) {
            return ResponseData.success(null);
        }
        // 排除BUS行程
        TravellerTripLimit travellerTripLimit = new TravellerTripLimit();
        travellerTripLimit.setExcludePlaneTyp(Sets.newHashSet("BUS"));
        // 查询会员行程信息
        List<TravellerTripInfo> travellerTripList = travellerTripService.getTravellerTrip(requestData, travellerTripLimit);
        if (CollectionUtils.isEmpty(travellerTripList)) {
            return ResponseData.success(null);
        }
        // 对会员行程进行排序
        travellerTripList = TravellerTripInfoMapper.sortList(travellerTripList);
        // 获取最近一个出现的航班行程
        TravellerTripInfo travellerTripInfo = travellerTripList.get(0);
        BffTravellerTripInfo bffTravellerTripInfo = new BffTravellerTripInfo();
        BeanUtils.copyProperties(travellerTripInfo, bffTravellerTripInfo);
        // 检查航班是否支持值机选座
        FlightSeatStatusParam flightSeatStatusParam = new FlightSeatStatusParam();
        flightSeatStatusParam.setFlightNo(bffTravellerTripInfo.getFlightNo());
        flightSeatStatusParam.setFlightDate(bffTravellerTripInfo.getFlightDate());
        flightSeatStatusParam.setDepAirportCode(bffTravellerTripInfo.getDepAirportCode());
        flightSeatStatusParam.setArrAirportCode(bffTravellerTripInfo.getArrAirportCode());
        FlightSeatStatusResult flightSeatState = cussBookingService.getFlightSeatStatus(BaseRequestUtil.createRequest(requestData, flightSeatStatusParam));
        bffTravellerTripInfo.setSegmentCanCheckIn(flightSeatState.isSegmentCanCheckIn());
        // 查询航班登机时间
        try {
            FlightBoardQueryBeanDTO flightBoardQueryBean = new FlightBoardQueryBeanDTO();
            flightBoardQueryBean.setFlightNo(bffTravellerTripInfo.getFlightNo());
            flightBoardQueryBean.setFlightDate(bffTravellerTripInfo.getFlightDate());
            flightBoardQueryBean.setDeptAirport(bffTravellerTripInfo.getDepAirportCode());
            flightBoardQueryBean.setArrAirport(bffTravellerTripInfo.getArrAirportCode());
            BoardInfoResultDTO boardInfoResult = cussCustomerService.queryFlightBoardInAirport(BaseRequestUtil.createRequest(requestData, flightBoardQueryBean));
            bffTravellerTripInfo.setBoardTime(boardInfoResult.getBoardingDateTime());
        } catch (Exception e) {

        }
        return ResponseData.success(bffTravellerTripInfo);
    }

}
