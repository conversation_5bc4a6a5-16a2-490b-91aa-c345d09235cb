package com.juneyaoair.oneorder.cuss.controller;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.juneyaoair.cuss.dto.booking.request.seat.v2.SeatRequest;
import com.juneyaoair.cuss.dto.booking.response.seat.v2.CheckInFailed;
import com.juneyaoair.cuss.dto.booking.response.seat.v2.ReserveSeatResult;
import com.juneyaoair.cuss.dto.booking.response.seat.v2.SeatChartResponseV2;
import com.juneyaoair.cuss.exception.CommonException;
import com.juneyaoair.cuss.param.seat.AddPeerParam;
import com.juneyaoair.cuss.request.QueryByMemberParam;
import com.juneyaoair.cuss.request.seat.SelectCheckInfoParam;
import com.juneyaoair.cuss.request.ticket.QuerySeatInfo;
import com.juneyaoair.cuss.request.ticket.TicketInfoParam;
import com.juneyaoair.cuss.response.seat.CheckInInfoResult;
import com.juneyaoair.cuss.response.seat.SeatOrderInfo;
import com.juneyaoair.cuss.response.seat.SelectCheckInfoResult;
import com.juneyaoair.cuss.response.seat.SelectInfoResult;
import com.juneyaoair.cuss.response.trip.TravellerTicketInfo;
import com.juneyaoair.cuss.response.trip.TravellerTripInfo;
import com.juneyaoair.cuss.response.trip.TravellerTripResult;
import com.juneyaoair.cuss.service.CussBookingService;
import com.juneyaoair.cuss.service.CussCustomerService;
import com.juneyaoair.mobile.exception.MultiLangServiceException;
import com.juneyaoair.mobile.exception.errorcode.CommonErrorCode;
import com.juneyaoair.oneorder.api.common.CommonService;
import com.juneyaoair.oneorder.api.geetest.sdk.enums.SceneEnum;
import com.juneyaoair.oneorder.api.geetest.service.IGeetestService;
import com.juneyaoair.oneorder.api.order.dto.coupon.QueryIsBuyInsuranceReq;
import com.juneyaoair.oneorder.common.dto.CheckDayLicense;
import com.juneyaoair.oneorder.common.dto.RequestDataDto;
import com.juneyaoair.oneorder.common.dto.enums.ChannelCodeEnum;
import com.juneyaoair.oneorder.common.dto.enums.CheckLicenseFuncEnum;
import com.juneyaoair.oneorder.common.enums.LanguageEnum;
import com.juneyaoair.oneorder.common.util.TraceUtil;
import com.juneyaoair.oneorder.cuss.bean.param.CancelSeatOrderParam;
import com.juneyaoair.oneorder.cuss.bean.param.EmdCancelSeat;
import com.juneyaoair.oneorder.cuss.bean.param.SendSeatVerifyCodeParam;
import com.juneyaoair.oneorder.cuss.bean.param.GetSeatContact;
import com.juneyaoair.oneorder.cuss.bean.param.QueryCheckInCountRequest;
import com.juneyaoair.oneorder.cuss.bean.param.RefundSeatOrderParam;
import com.juneyaoair.oneorder.cuss.bean.param.ReserveSeatParam;
import com.juneyaoair.oneorder.cuss.bean.param.SeatMapInfoParam;
import com.juneyaoair.oneorder.cuss.bean.param.SelectSeatInfoParam;
import com.juneyaoair.oneorder.cuss.bean.result.*;
import com.juneyaoair.oneorder.cuss.mapstruct.SeatMapper;
import com.juneyaoair.oneorder.cuss.mapstruct.TravellerTripInfoMapper;
import com.juneyaoair.oneorder.cuss.service.SeatBaseService;
import com.juneyaoair.oneorder.cuss.service.SeatService;
import com.juneyaoair.oneorder.cuss.utils.BaseRequestUtil;
import com.juneyaoair.oneorder.mobile.config.RedisConstantConfig;
import com.juneyaoair.oneorder.mobile.utils.RedisUtil;
import com.juneyaoair.oneorder.restresult.response.ResponseData;
import com.juneyaoair.oneorder.util.LoginCheckUtil;
import com.juneyaoair.oneorder.util.MetricLogUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 值机选座服务
 * @created 2023/11/15 17:24
 */
@Slf4j
@Api(value = "值机选座服务")
@RestController
public class SeatController {

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private SeatService seatService;
    @Autowired
    private CommonService commonService;
    @Autowired
    private SeatBaseService seatBaseService;
    @Autowired
    private IGeetestService geetestService;
    @Autowired
    private CussBookingService cussBookingService;
    @Autowired
    private CussCustomerService cussCustomerService;

    @ApiOperation(value = "查询航班座位图V2")
    @PostMapping("/seat/querySeatMap")
    public ResponseData<SeatMapResult> querySeatMap(@RequestBody @Validated RequestDataDto<SeatRequest> seatRequestRequestData) {
        // 查询航班座位图 强制刷新、锁定安全出口排座位
        SeatRequest seatRequest = seatRequestRequestData.getData();
        seatRequest.setLockTagSet(Sets.newHashSet("E"));
        SeatChartResponseV2 seatChartResponse = cussBookingService.querySeatMap(BaseRequestUtil.createRequest(seatRequestRequestData, seatRequest));
        SeatMapResult seatMapResult = new SeatMapResult();
        seatMapResult.setSeatCharts(seatChartResponse);
        seatMapResult.setSeatDiscount(seatChartResponse.getSeatDiscount());
        Future<SeatSelectInfo> selectSeatInfoFuture = null;
        String traceId = TraceUtil.getTraceId();
        // 票号不为空 获取当前票号选座信息
        if (StringUtils.isNotEmpty(seatRequest.getTicketNo())) {
            if (StringUtils.isBlank(seatRequest.getPassengerName())) {
                log.info("姓名不能为空，票号：{} 操作人：{}", seatRequest.getTicketNo(), seatRequestRequestData.getFfpNo());
                throw new MultiLangServiceException(CommonErrorCode.REQUEST_VALIDATION_FAILED, "姓名不能为空！");
            }
            TicketInfoParam ticketInfoParam = SeatMapper.MAPPER.getTicketInfoParam(seatRequest);
            selectSeatInfoFuture = seatBaseService.getSelectSeatInfo(seatRequestRequestData, traceId, ticketInfoParam);
            MetricLogUtils.saveMetricLog("航班座位图V2-客票提取", seatRequestRequestData);
        }
        if (null != selectSeatInfoFuture) {
            try {
                SeatSelectInfo selectSeatInfo = selectSeatInfoFuture.get();
                seatMapResult.setSelectSeatInfo(selectSeatInfo);
            } catch (InterruptedException ie) {
                log.info("1.获取票号选座信息异常，请求参数：{} 异常信息：", JSON.toJSONString(seatRequest), ie);
                Thread.currentThread().interrupt();
                throw new MultiLangServiceException(CommonErrorCode.SYSTEM_ERROR, "获取客票信息异常！");
            } catch (Exception e) {
                log.info("2.获取票号选座信息异常，请求参数：{} 异常信息：", JSON.toJSONString(seatRequest), e);
                if (e.getCause() instanceof CommonException) {
                    throw (CommonException) e.getCause();
                }
                throw new MultiLangServiceException(CommonErrorCode.SYSTEM_ERROR, "获取客票信息异常！");
            }
        }
        return ResponseData.success(seatMapResult);
    }

    @ApiOperation(value = "查询航班座位图V3")
    @PostMapping("/seat/querySeatMap/V3")
    public ResponseData<SeatMapInfoBff> querySeatMapV3(@RequestBody @Validated RequestDataDto<SeatMapInfoParam> requestData) {
        // 检查接口是否访问合规
        LoginCheckUtil.checkLogin(requestData, ChannelCodeEnum.G_B2C);
        // 未登录情况下限制IP访问次数
        if (!requestData.isLoginFlag()) {
            // 限制IP访问次数
            CheckDayLicense ipCheckDayLicense = new CheckDayLicense(requestData.getOriginIp(), CheckLicenseFuncEnum.GLOBAL_SEAT_MAP_IP, "查询座位图失败，达到单日IP上限");
            commonService.checkDayLicense(true, ipCheckDayLicense);
        }
        return ResponseData.success(seatService.querySeatMap(requestData));
    }

    @ApiOperation(value = "查询并校验多人是否支持选座")
    @PostMapping("/seat/getSelectSeatInfoCacheId")
    public ResponseData<SelectSeatInfoCache> getSelectSeatInfoCacheId(@RequestBody @Validated RequestDataDto<SelectSeatInfoParam> requestData) {
        SelectSeatInfoParam selectSeatInfoParam = requestData.getData();
        MetricLogUtils.saveMetricLog("订单页值机选座预操作-客票提取", requestData);
        List<Future<SeatSelectInfo>> futureList = Lists.newArrayList();
        for (SelectSeatInfoParam.Traveller traveller : selectSeatInfoParam.getTravellerList()) {
            AddPeerParam addPeerParam = new AddPeerParam();
            addPeerParam.setFlightNo(selectSeatInfoParam.getFlightNo());
            addPeerParam.setFlightDate(selectSeatInfoParam.getFlightDate());
            addPeerParam.setDepAirportCode(selectSeatInfoParam.getDepAirportCode());
            addPeerParam.setArrAirportCode(selectSeatInfoParam.getArrAirportCode());
            addPeerParam.setPeerCertNo(traveller.getTicketNo());
            addPeerParam.setPeerName(traveller.getTravellerName());
            Future<SeatSelectInfo> seatSelectInfoResultFuture = seatBaseService.addPeer(requestData, TraceUtil.getTraceId(), addPeerParam);
            futureList.add(seatSelectInfoResultFuture);
        }
        List<SeatSelectInfo> seatSelectInfoList = Lists.newArrayList();
        for (Future<SeatSelectInfo> future : futureList) {
            try {
                SeatSelectInfo selectSeatInfo = future.get();
                seatSelectInfoList.add(selectSeatInfo);
            } catch (InterruptedException ie) {
                Thread.currentThread().interrupt();
                throw new MultiLangServiceException(CommonErrorCode.SYSTEM_ERROR, "获取客票信息异常[1]");
            } catch (Exception e) {
                if (e.getCause() instanceof CommonException) {
                    throw (CommonException) e.getCause();
                }
                log.info("获取票号选座信息异常，异常信息：", e);
                throw new MultiLangServiceException(CommonErrorCode.SYSTEM_ERROR, "获取客票信息异常[2]");
            }
        }
        String cacheId = UUID.randomUUID().toString();
        final String redisKey = RedisConstantConfig.SELECT_SEAT_INFO_CACHE + requestData.getFfpNo() + ":" + cacheId;
        redisUtil.setJSON(redisKey, seatSelectInfoList, 600L);
        SelectSeatInfoCache selectSeatInfoCache = new SelectSeatInfoCache();
        selectSeatInfoCache.setCacheId(cacheId);
        return ResponseData.success(selectSeatInfoCache);
    }

    @ApiOperation(value = "多人选座、值机")
    @PostMapping("/seat/reserveSeat")
    public ResponseData<ReserveSeatResult> reserveSeat(@RequestBody @Validated RequestDataDto<ReserveSeatParam> requestData) {
        // 检查接口是否访问合规
        LoginCheckUtil.checkLogin(requestData, ChannelCodeEnum.G_B2C);
        ReserveSeatParam reserveSeatParam = requestData.getData();
        // 未传币种 使用外层页面币种
        if (StringUtils.isBlank(reserveSeatParam.getCurrency())) {
            reserveSeatParam.setCurrency(requestData.getCurrency());
        }
        // 未登录情况下限制IP访问次数
        if (!requestData.isLoginFlag()) {
            // 限制IP访问次数
            CheckDayLicense ipCheckDayLicense = new CheckDayLicense(requestData.getOriginIp(), CheckLicenseFuncEnum.GLOBAL_RESERVE_SEAT_IP, "值机选座失败，达到单日IP上限");
            commonService.checkDayLicense(true, ipCheckDayLicense);
            // 使用积分需要登录
            if (reserveSeatParam.getUseTotalScore().compareTo(BigDecimal.ZERO) > 0) {
                throw new MultiLangServiceException(CommonErrorCode.NOT_LOGIN);
            }
            // 未登录用户 会员信息设置默认值-2
            requestData.setFfpId("-2");
            requestData.setFfpNo("-2");
        }
        ReserveSeatResult reserveSeatResult = seatService.reserveSeat(requestData);
        List<CheckInFailed> failedSeatResultList = reserveSeatResult.getFailedSeatResultList();
        // 不存在失败数据 或非中文值机返回
        if (CollectionUtils.isEmpty(failedSeatResultList) || !LanguageEnum.ZH_CN.equals(requestData.getLanguage())) {
            return ResponseData.success(reserveSeatResult);
        }
        List<String> nameSt = failedSeatResultList.stream().map(CheckInFailed::getPsgrName).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(nameSt)) {
            return ResponseData.success(reserveSeatResult);
        }
        String nameString = StringUtils.join(nameSt, "、");
        String nameResult = String.format(CommonErrorCode.CHECK_IN_FAILED.getMessage(), nameString);
        return ResponseData.success(CommonErrorCode.CHECK_IN_FAILED.getCode(), CommonErrorCode.CHECK_IN_FAILED.getStatus(), nameResult, reserveSeatResult);
    }

    @ApiOperation(value = "获取选座联系方式信息")
    @PostMapping(value = "/seat/getSeatPhone")
    public ResponseData<SeatContactInfo> getSeatPhone(@RequestBody @Validated RequestDataDto<GetSeatContact> requestData) {
        // 检查接口是否访问合规
        LoginCheckUtil.checkLogin(requestData, ChannelCodeEnum.G_B2C);
        // 未登录情况下限制IP访问次数
        if (!requestData.isLoginFlag()) {
            // 限制IP访问次数
            CheckDayLicense ipCheckDayLicense = new CheckDayLicense(requestData.getOriginIp(), CheckLicenseFuncEnum.GLOBAL_GET_SEAT_CONTACT_INFO_IP, "获取选座联系信息失败，达到单日IP上限");
            commonService.checkDayLicense(true, ipCheckDayLicense);
        }
        return ResponseData.success(seatService.getSeatContactInfo(requestData));
    }

    @ApiOperation(value = "取消选座验证码发送(极验CLIENT_TYPE使用seatSms)")
    @PostMapping(value = "/seat/sendVerifyCode")
    public ResponseData<SeatContactInfo> sendVerifyCode(@RequestBody @Validated RequestDataDto<SendSeatVerifyCodeParam> requestData, HttpServletRequest request) {
        // 检查接口是否访问合规
        LoginCheckUtil.checkLogin(requestData, ChannelCodeEnum.G_B2C);
        SendSeatVerifyCodeParam verifyCodeParam = requestData.getData();
        // 入参存在极验参数优先使用外层极验参数
        if (StringUtils.isNotBlank(requestData.getGeetest_challenge())) {
            verifyCodeParam.setGeetest_challenge(requestData.getGeetest_challenge());
        }
        if (StringUtils.isNotBlank(requestData.getGeetest_validate())) {
            verifyCodeParam.setGeetest_validate(requestData.getGeetest_validate());
        }
        if (StringUtils.isNotBlank(requestData.getGeetest_seccode())) {
            verifyCodeParam.setGeetest_seccode(requestData.getGeetest_seccode());
        }
        if (StringUtils.isNotBlank(requestData.getClient_type())) {
            verifyCodeParam.setClient_type(requestData.getClient_type());
        }
        // 极验验证
        geetestService.validate(SceneEnum.SEAT_SMS, verifyCodeParam);
        SeatContactInfo seatContactInfo = seatService.sendVerifyCode(requestData, verifyCodeParam, requestData.getData(), request);
        return ResponseData.success(seatContactInfo);
    }

    @ApiOperation(value = "系统取消选座、值机（适用于免费选座)")
    @PostMapping(value = "/seat/cancelSeat")
    public ResponseData<Object> cancelSeat(@RequestBody @Validated RequestDataDto<EmdCancelSeat> requestData) {
        // 检查接口是否访问合规
        LoginCheckUtil.checkLogin(requestData, ChannelCodeEnum.G_B2C);
        // 未登录情况下限制IP访问次数
        if (!requestData.isLoginFlag()) {
            // 限制IP访问次数
            CheckDayLicense ipCheckDayLicense = new CheckDayLicense(requestData.getOriginIp(), CheckLicenseFuncEnum.GLOBAL_CANCEL_SEAT_IP, "取消值机选座操作失败，达到单日IP上限");
            commonService.checkDayLicense(true, ipCheckDayLicense);
            // 未登录用户 会员信息设置默认值-2
            requestData.setFfpId("-2");
            requestData.setFfpNo("-2");
        }
        seatService.cancelSeat(requestData);
        return ResponseData.success(null);
    }

    @ApiOperation(value = "付费选座取消订单（未支付情况下）")
    @PostMapping(value = "/seat/cancelSeatOrder")
    public ResponseData<String> cancelSeatOrder(@RequestBody @Validated RequestDataDto<CancelSeatOrderParam> requestData) {
        // 检查接口是否访问合规
        LoginCheckUtil.checkLogin(requestData, ChannelCodeEnum.G_B2C);
        // 未登录情况下限制IP访问次数
        if (!requestData.isLoginFlag()) {
            // 限制IP访问次数
            CheckDayLicense ipCheckDayLicense = new CheckDayLicense(requestData.getOriginIp(), CheckLicenseFuncEnum.GLOBAL_CANCEL_SEAT_ORDER_IP, "取消优先选座订单失败，达到单日IP上限");
            commonService.checkDayLicense(true, ipCheckDayLicense);
            // 未登录用户 会员信息设置默认值-2
            requestData.setFfpId("-2");
            requestData.setFfpNo("-2");
        }
        seatService.cancelSeatOrder(requestData);
        return ResponseData.success(null);
    }

    @ApiOperation(value = "付费选座退单")
    @PostMapping(value = "/seat/refundSeatOrder")
    public ResponseData<String> refundSeatOrder(@RequestBody @Validated RequestDataDto<RefundSeatOrderParam> requestData) {
        // 检查接口是否访问合规
        LoginCheckUtil.checkLogin(requestData, ChannelCodeEnum.G_B2C);
        // 未登录情况下限制IP访问次数
        if (!requestData.isLoginFlag()) {
            // 限制IP访问次数
            CheckDayLicense ipCheckDayLicense = new CheckDayLicense(requestData.getOriginIp(), CheckLicenseFuncEnum.GLOBAL_REFUND_SEAT_ORDER_IP, "优先选座退单失败，达到单日IP上限");
            commonService.checkDayLicense(true, ipCheckDayLicense);
            // 未登录用户 会员信息设置默认值-2
            requestData.setFfpId("-2");
            requestData.setFfpNo("-2");
        }
        seatService.refundSeatOrder(requestData);
        return ResponseData.success(null);
    }

    @ApiOperation(value = "添加同行人")
    @PostMapping(value = "/seat/addPeer")
    public ResponseData<SeatSelectInfo> addPeer(@RequestBody @Validated RequestDataDto<AddPeerParam> requestData) {
        // 检查接口是否访问合规
        LoginCheckUtil.checkLogin(requestData, ChannelCodeEnum.G_B2C);
        // 未登录情况下限制IP访问次数
        if (!requestData.isLoginFlag()) {
            // 限制IP访问次数
            CheckDayLicense ipCheckDayLicense = new CheckDayLicense(requestData.getOriginIp(), CheckLicenseFuncEnum.GLOBAL_REFUND_SEAT_ORDER_IP, "添加同行人失败，达到单日IP上限");
            commonService.checkDayLicense(true, ipCheckDayLicense);
            // 未登录用户 会员信息设置默认值-2
            requestData.setFfpId("-2");
            requestData.setFfpNo("-2");
        }
        SeatSelectInfo seatSelectInfo = seatService.addPeer(requestData);
        return ResponseData.success(seatSelectInfo);
    }

    @ApiIgnore
    @ApiOperation(value = "查询值机结果信息(不建议使用 使用接口/seat/getSeatCheckInfo替代)")
    @PostMapping(value = "/seat/queryCheckInInfo")
    public ResponseData<CheckInInfoResult> queryCheckInInfo(@RequestBody @Validated RequestDataDto<QuerySeatInfo> querySeatInfoRequestData) {
        return ResponseData.success(cussBookingService.queryCheckInInfo(BaseRequestUtil.createRequest(querySeatInfoRequestData)));
    }

    @ApiIgnore
    @ApiOperation(value = "查询选座结果信息(不建议使用 使用接口/seat/getSeatCheckInfo替代)")
    @PostMapping(value = "/seat/querySelectInfo")
    public ResponseData<SelectInfoResult> querySelectInfo(@RequestBody @Validated RequestDataDto<QuerySeatInfo> querySeatInfoRequestData) {
        return ResponseData.success(cussBookingService.querySelectInfo(BaseRequestUtil.createRequest(querySeatInfoRequestData)));
    }

    @ApiOperation(value = "查询旅客值机次数并进行校验")
    @PostMapping(value = "/seat/queryCheckInCount")
    public ResponseData<CheckInCountCheckResult> queryCheckInCount(@RequestBody @Validated RequestDataDto<QueryCheckInCountRequest> requestData) {
        CheckInCountCheckResult checkInCountCheckResult = seatService.getCheckInCountCheckResult(requestData);
        return ResponseData.success(checkInCountCheckResult);
    }

    @ApiOperation(value = "选座值机记录")
    @PostMapping(value = "/seat/querySeatRecord")
    public ResponseData<TravellerTripResult> querySeatRecord(@RequestBody @Validated RequestDataDto<Object> queryByMemberParamRequestData) {
        QueryByMemberParam queryByMemberParam = new QueryByMemberParam();
        queryByMemberParam.setMemberId(queryByMemberParamRequestData.getFfpId());
        queryByMemberParam.setFfpCardNo(queryByMemberParamRequestData.getFfpNo());
        TravellerTripResult travellerTripResult = cussBookingService.querySeatRecord(BaseRequestUtil.createRequest(queryByMemberParamRequestData, queryByMemberParam));
        // 待支付选座
        Map<String, TravellerTripInfo> unPaySeatMap = Maps.newHashMap();
        // 已选座
        Map<String, TravellerTripInfo> seatMap = Maps.newHashMap();
        travellerTripResult.getTravellerTripInfoList().forEach(travellerTripInfo -> {
            for (TravellerTicketInfo travellerTicketInfo : travellerTripInfo.getTravellerList()) {
                SeatOrderInfo seatOrderInfo = travellerTicketInfo.getSeatOrderInfo();
                // 不存在订单 放入已选座清单
                if (null == seatOrderInfo) {
                    TravellerTripInfoMapper.setMapValue(seatMap, travellerTripInfo, travellerTicketInfo);
                }
                // 待支付订单
                else if ("UN_PAY".equals(seatOrderInfo.getSeatState())) {
                    TravellerTripInfoMapper.setMapValue(unPaySeatMap, travellerTripInfo, travellerTicketInfo);
                } else {
                    TravellerTripInfoMapper.setMapValue(seatMap, travellerTripInfo, travellerTicketInfo);
                }
            }
        });
        // 针对待支付选座、可选座、已选座、不可选座数据进行排序整合
        List<TravellerTripInfo> unPaySeatList = TravellerTripInfoMapper.sortList(unPaySeatMap.values());
        List<TravellerTripInfo> seatList = TravellerTripInfoMapper.sortList(seatMap.values());
        List<TravellerTripInfo> travellerTripInfoResultList = Lists.newArrayList();
        travellerTripInfoResultList.addAll(unPaySeatList);
        travellerTripInfoResultList.addAll(seatList);
        travellerTripResult.setTravellerTripInfoList(travellerTripInfoResultList);
        return ResponseData.success(travellerTripResult);
    }

    @ApiOperation(value = "查询选座值机信息")
    @PostMapping(value = "/seat/getSeatCheckInfo")
    public ResponseData<SelectCheckInfoResult> getSeatCheckInfo(@RequestBody @Validated RequestDataDto<SelectCheckInfoParam> requestData) {
        // 检查接口是否访问合规
        LoginCheckUtil.checkLogin(requestData, ChannelCodeEnum.G_B2C);
        // 未登录情况下限制IP访问次数
        if (!requestData.isLoginFlag()) {
            // 限制IP访问次数
            CheckDayLicense ipCheckDayLicense = new CheckDayLicense(requestData.getOriginIp(), CheckLicenseFuncEnum.GLOBAL_GET_SEAT_CHECK_INFO_IP, "查询选座值机信息失败，达到单日IP上限");
            commonService.checkDayLicense(true, ipCheckDayLicense);
        }
        MetricLogUtils.saveMetricLog("选座值机信息页-客票提取", requestData);
        return ResponseData.success(seatService.getSeatCheckInfo(BaseRequestUtil.createRequest(requestData)));
    }

    @ApiOperation(value = "选座保险")
    @PostMapping(value = "/seat/getSeatInsure")
    public ResponseData<SeatInsureResult> getSeatInsure(@RequestBody @Validated RequestDataDto<QueryIsBuyInsuranceReq> requestData) {
        SeatInsureResult seatInsureResult = seatService.getSeatInsure(requestData);
        return ResponseData.success(null == seatInsureResult ? new SeatInsureResult() : seatInsureResult);
    }

}
