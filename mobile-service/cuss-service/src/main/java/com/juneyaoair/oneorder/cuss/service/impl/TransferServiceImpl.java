package com.juneyaoair.oneorder.cuss.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.juneyaoair.flightbasic.response.api.ApiAirPortInfoDto;
import com.juneyaoair.flightbasic.response.country.TCountryDTO;
import com.juneyaoair.flightbasic.route.RouteLabelDTO;
import com.juneyaoair.oneorder.api.basic.service.IBasicService;
import com.juneyaoair.oneorder.api.common.CacheService;
import com.juneyaoair.oneorder.api.dsop.dto.TransferPassengerParam;
import com.juneyaoair.oneorder.api.dsop.dto.TransferPassengerResult;
import com.juneyaoair.oneorder.api.dsop.service.IDsopService;
import com.juneyaoair.oneorder.cuss.bean.result.TransferFlightInfo;
import com.juneyaoair.oneorder.cuss.bean.result.TransferStayTripInfo;
import com.juneyaoair.oneorder.cuss.bean.result.TransferStayTripResult;
import com.juneyaoair.oneorder.cuss.service.TransferService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 中转旅客
 * <AUTHOR>
 */
@Slf4j
@Service
public class TransferServiceImpl implements TransferService {

    @Autowired
    private IBasicService basicService;
    @Autowired
    private CacheService cacheService;
    @Autowired
    private IDsopService dsopService;

    @Override
    public TransferStayTripResult getTransferStayTrip(String channelCode, Set<String> certNoSet, String name) {
        List<TransferPassengerResult> transferPassengerList = Lists.newArrayList();
        // 查询旅客中转行程
        TransferPassengerParam transferPassengerParam = new TransferPassengerParam();
        transferPassengerParam.setPageNum(1);
        transferPassengerParam.setPageSize(2000);
        transferPassengerParam.setFlightsGroupFlag("0");
        LocalDateTime localDateTime = LocalDateTime.now().plusDays(-3);
        String beforeDay4 = DateUtil.format(localDateTime, DatePattern.NORM_DATE_PATTERN);
        transferPassengerParam.setDepartureDateCnNo1Begin(beforeDay4);
        for (String certNo : certNoSet) {
            transferPassengerParam.setPassportNumber(certNo);
            List<TransferPassengerResult> list = dsopService.transferPassenger(transferPassengerParam);
            if (CollectionUtils.isNotEmpty(list)) {
                transferPassengerList.addAll(list);
            }
        }
        if (CollectionUtils.isEmpty(transferPassengerList)) {
            return null;
        }
        // 查询航班中转住宿标签信息
        List<RouteLabelDTO> routeLabelList = basicService.queryRouteLabel(channelCode, "TransitAccommodation");
        if (CollectionUtils.isEmpty(routeLabelList)) {
            log.info("中转住宿旅客不存在中转住宿标签信息");
            return null;
        }
        List<TransferStayTripInfo> transferList = Lists.newArrayList();
        for (TransferPassengerResult transferPassenger : transferPassengerList) {
            // 第一程信息
            TransferFlightInfo flightInfo1 = new TransferFlightInfo();
            flightInfo1.setFlightNo(transferPassenger.getOpcarrierFlightNo1());
            flightInfo1.setOperationAirLine(transferPassenger.getOpcarrierAirlinecodeNo1());
            flightInfo1.setFlightDate(transferPassenger.getDepartureDateNo1Begin());
            flightInfo1.setDepAirportCode(transferPassenger.getDepartureAirportcode());
            ApiAirPortInfoDto depAirportInfo1 = cacheService.getLocalAirport(flightInfo1.getDepAirportCode());
            flightInfo1.setDepAirportName(depAirportInfo1.getAirPortName());
            flightInfo1.setDepCity(depAirportInfo1.getCityCode());
            flightInfo1.setDepCityName(depAirportInfo1.getCityName());
            // 根据第一程出发北京时间以及时间差获取第一程出发当地时间
            Date segDepTime1 = DateUtil.parse(transferPassenger.getStdNo1());
            segDepTime1 = DateUtil.offsetHour(segDepTime1, transferPassenger.getDepTimeDiffNo1());
            flightInfo1.setDepTime(DateUtil.format(segDepTime1, DatePattern.NORM_DATETIME_MINUTE_PATTERN));
            flightInfo1.setArrAirportCode(transferPassenger.getArrivalAirportcodeNo1());
            ApiAirPortInfoDto arrAirportInfo1 = cacheService.getLocalAirport(flightInfo1.getArrAirportCode());
            flightInfo1.setArrAirportName(arrAirportInfo1.getAirPortName());
            flightInfo1.setArrCity(arrAirportInfo1.getCityCode());
            flightInfo1.setArrCityName(arrAirportInfo1.getCityName());
            // 根据第一程到达北京时间以及时间差获取第一程到达当地时间
            Date segArrTime1 = DateUtil.parse(transferPassenger.getStaNo1());
            segArrTime1 = DateUtil.offsetHour(segArrTime1, transferPassenger.getArrTimeDiffNo1());
            flightInfo1.setArrTime(DateUtil.format(segArrTime1, DatePattern.NORM_DATETIME_MINUTE_PATTERN));

            // 第二程信息
            TransferFlightInfo flightInfo2 = new TransferFlightInfo();
            flightInfo2.setFlightNo(transferPassenger.getOpcarrierFlightNo2());
            flightInfo2.setOperationAirLine(transferPassenger.getOpcarrierAirlinecodeNo2());
            flightInfo2.setFlightDate(transferPassenger.getDepartureDateNo2Begin());
            flightInfo2.setDepAirportCode(transferPassenger.getDepartureAirportcodeNo2());
            ApiAirPortInfoDto depAirportInfo2 = cacheService.getLocalAirport(flightInfo2.getDepAirportCode());
            flightInfo2.setDepAirportName(depAirportInfo2.getAirPortName());
            flightInfo2.setDepCity(depAirportInfo2.getCityCode());
            flightInfo2.setDepCityName(depAirportInfo2.getCityName());
            // 根据第一程出发北京时间以及时间差获取第一程出发当地时间
            Date segDepTime2 = DateUtil.parse(transferPassenger.getStdNo2());
            segDepTime2 = DateUtil.offsetHour(segDepTime2, transferPassenger.getDepTimeDiffNo2());
            flightInfo2.setDepTime(DateUtil.format(segDepTime2, DatePattern.NORM_DATETIME_MINUTE_PATTERN));
            flightInfo2.setArrAirportCode(transferPassenger.getArrivalAirportcode());
            ApiAirPortInfoDto arrAirportInfo2 = cacheService.getLocalAirport(flightInfo2.getArrAirportCode());
            flightInfo2.setArrAirportName(arrAirportInfo2.getAirPortName());
            flightInfo2.setArrCity(arrAirportInfo2.getCityCode());
            flightInfo2.setArrCityName(arrAirportInfo2.getCityName());
            // 根据第一程到达北京时间以及时间差获取第一程到达当地时间
            Date segArrTime2 = DateUtil.parse(transferPassenger.getStaNo2());
            segArrTime2 = DateUtil.offsetHour(segArrTime2, transferPassenger.getArrTimeDiffNo2());
            flightInfo2.setArrTime(DateUtil.format(segArrTime2, DatePattern.NORM_DATETIME_MINUTE_PATTERN));

            if (!isCanApply(flightInfo1, flightInfo2, routeLabelList)) {
                log.info("中转住宿旅客中转住宿标签校验不通过，客票：{}", transferPassenger.getTicketnumber());
                continue;
            }
            TransferStayTripInfo transferStayTripInfo = new TransferStayTripInfo();
            transferStayTripInfo.setTicketNo(transferPassenger.getTicketnumber());
            transferStayTripInfo.setPassengerName(transferPassenger.getLastname() + "/" + transferPassenger.getFirstname());
            DateTime stdNo1 = DateUtil.parse(transferPassenger.getStdNo1());
            DateTime staNo2 = DateUtil.parse(transferPassenger.getStaNo2());
            long totalDuration = DateUtil.between(stdNo1, staNo2, DateUnit.MINUTE);
            transferStayTripInfo.setTotalDuration(totalDuration);
            List<TransferFlightInfo> transferFlightList = Lists.newArrayList();
            transferFlightList.add(flightInfo1);
            transferFlightList.add(flightInfo2);
            transferStayTripInfo.setTransferFlightList(transferFlightList);
            transferList.add(transferStayTripInfo);
        }
        if (CollectionUtils.isEmpty(transferList)) {
            return null;
        }
        transferList = transferList.stream().sorted(
                Comparator.comparing(transferStayTripInfo -> transferStayTripInfo.getTransferFlightList().isEmpty() ? null :
                        (null == transferStayTripInfo.getTransferFlightList().get(0) ? null : transferStayTripInfo.getTransferFlightList().get(0).getDepTime()),
                        Comparator.nullsLast(String::compareTo))
        ).collect(Collectors.toList());
        TransferStayTripResult transferStayTripResult = new TransferStayTripResult();
        transferStayTripResult.setTransferStayTripList(transferList);
        return transferStayTripResult;
    }

    /**
     * 基于规则校验是否可申请中转住宿
     * @param flightInfo1
     * @param flightInfo2
     * @param routeLabelList
     * @return
     */
    public boolean isCanApply(TransferFlightInfo flightInfo1, TransferFlightInfo flightInfo2, List<RouteLabelDTO> routeLabelList) {
        // 出发机场信息
        ApiAirPortInfoDto depAirportInfo = cacheService.getLocalAirport(flightInfo1.getDepAirportCode());
        TCountryDTO depAirportCountry = basicService.queryCountry(depAirportInfo.getCountryNo());
        // 中转机场信息
        ApiAirPortInfoDto transferAirportInfo = cacheService.getLocalAirport(flightInfo2.getDepAirportCode());
        // 到达机场信息
        ApiAirPortInfoDto arrAirportInfo = cacheService.getLocalAirport(flightInfo2.getArrAirportCode());
        TCountryDTO arrAirportCountry = basicService.queryCountry(arrAirportInfo.getCountryNo());

        Date segDepTime1 = DateUtil.parse(flightInfo1.getDepTime());
        Date segArrTime1 = DateUtil.parse(flightInfo1.getArrTime());
        Date segDepTime2 = DateUtil.parse(flightInfo2.getDepTime());
        String operationAirLine1 = flightInfo1.getOperationAirLine();
        String operationAirLine2 = flightInfo2.getOperationAirLine();
        // 计算中转时长
        long transferTimeMinute = DateUtil.between(segArrTime1, segDepTime2, DateUnit.MINUTE);
        // 判断第一程、第二程是否跨天
        boolean segOverNight = !DateUtil.formatDate(segDepTime1).equals(DateUtil.formatDate(segDepTime2));
        // 检查是否符合规则
        for (RouteLabelDTO label : routeLabelList) {
            for (RouteLabelDTO.RouteLabelRuleDTO rule : label.getRuleDTOList()) {
                // 第一程、第二程出发时间是否均在规则生效期内 不在有效期内处理下一个数据
                if (!isDateRangeValid(segDepTime1, segDepTime2, rule)) {
                    continue;
                }
                // 出发机场是否在规则内 不在规则内处理下一个数据
                if (!isDepAirportValid(rule, depAirportInfo)) {
                    continue;
                }
                // 出发机场所在国家是否在规则内 不在规则内处理下一个数据
                if (!isDepCountryValid(rule, depAirportInfo)) {
                    continue;
                }
                // 出发机场所在洲是否在规则内 不在规则内处理下一个数据
                if (!isDepRegionValid(rule, depAirportCountry)) {
                    continue;
                }
                // 到达机场是否在规则内  不在规则内处理下一个数据
                if (!isArrAirportValid(rule, arrAirportInfo)) {
                    continue;
                }
                // 到达机场所在国家是否在规则内 不在规则内处理下一个数据
                if (!isArrCountryValid(rule, arrAirportInfo)) {
                    continue;
                }
                // 到达机场所在洲是否在规则内 不在规则内处理下一个数据
                if (!isArrRegionValid(rule, arrAirportCountry)) {
                    continue;
                }
                // 中转机场是否在规则内 不在规则内处理下一个数据
                if (!isTransAirportValid(rule, transferAirportInfo)) {
                    continue;
                }
                // 中转时长是否在规则内 不在规则内处理下一个数据
                if (!isTransferTimeValid(rule, transferTimeMinute)) {
                    continue;
                }
                // 承运航司是否在规则内 不在规则内处理下一个数据
                if (!isCarrierValid(rule, operationAirLine1, operationAirLine2)) {
                    continue;
                }
                // 是否跨天是否在规则有效期内 不在规则内处理下一个数据
                if (!isDateLimitValid(rule, segOverNight)) {
                    continue;
                }
                return true;
            }
        }
        return false;
    }

    /**
     * 第一程、第二程出发时间是否均在规则有效期内
     * @param seg1DepTime
     * @param seg2DepTime
     * @param rule
     * @return
     */
    private boolean isDateRangeValid(Date seg1DepTime, Date seg2DepTime, RouteLabelDTO.RouteLabelRuleDTO rule) {
        return DateUtil.isIn(seg1DepTime, DateUtil.parse(rule.getRouteStartDate()), DateUtil.parse(rule.getRouteEndDate())) &&
                DateUtil.isIn(seg2DepTime, DateUtil.parse(rule.getRouteStartDate()), DateUtil.parse(rule.getRouteEndDate()));
    }

    /**
     * 出发机场是否在规则内
     * @param rule
     * @param depAirportInfo
     * @return
     */
    private boolean isDepAirportValid(RouteLabelDTO.RouteLabelRuleDTO rule, ApiAirPortInfoDto depAirportInfo) {
        return Strings.isNullOrEmpty(rule.getDepAirport()) || rule.getDepAirport().contains(depAirportInfo.getAirPortCode());
    }

    /**
     * 出发机场所在国家是否在规则内
     * @param rule
     * @param depAirportInfo
     * @return
     */
    private boolean isDepCountryValid(RouteLabelDTO.RouteLabelRuleDTO rule, ApiAirPortInfoDto depAirportInfo) {
        return Strings.isNullOrEmpty(rule.getDepCountry()) || rule.getDepCountry().contains(depAirportInfo.getCountryNo());
    }

    /**
     * 出发机场所在洲是否在规则内
     * @param rule
     * @param depCountry
     * @return
     */
    private boolean isDepRegionValid(RouteLabelDTO.RouteLabelRuleDTO rule, TCountryDTO depCountry) {
        return Strings.isNullOrEmpty(rule.getDepRegion()) || rule.getDepRegion().equals(depCountry.getRegionCode());
    }

    /**
     * 到达机场是否在规则内
     * @param rule
     * @param arrAirportInfo
     * @return
     */
    private boolean isArrAirportValid(RouteLabelDTO.RouteLabelRuleDTO rule, ApiAirPortInfoDto arrAirportInfo) {
        return Strings.isNullOrEmpty(rule.getArrAirport()) || rule.getArrAirport().contains(arrAirportInfo.getAirPortCode());
    }

    /**
     * 到达机场所在国家是否在规则内
     * @param rule
     * @param arrAirportInfo
     * @return
     */
    private boolean isArrCountryValid(RouteLabelDTO.RouteLabelRuleDTO rule, ApiAirPortInfoDto arrAirportInfo) {
        return Strings.isNullOrEmpty(rule.getArrCountry()) || rule.getArrCountry().contains(arrAirportInfo.getCountryNo());
    }

    /**
     * 到达机场所在洲是否在规则内
     * @param rule
     * @param arrCountry
     * @return
     */
    private boolean isArrRegionValid(RouteLabelDTO.RouteLabelRuleDTO rule, TCountryDTO arrCountry) {
        return Strings.isNullOrEmpty(rule.getArrRegion()) || rule.getArrRegion().equals(arrCountry.getRegionCode());
    }

    /**
     * 中转机场是否在规则内
     * @param rule
     * @param transferAirportInfo
     * @return
     */
    private boolean isTransAirportValid(RouteLabelDTO.RouteLabelRuleDTO rule, ApiAirPortInfoDto transferAirportInfo) {
        return Strings.isNullOrEmpty(rule.getTransAirport()) || rule.getTransAirport().contains(transferAirportInfo.getAirPortCode());
    }

    /**
     * 中转时长是否在规则内
     * @param rule
     * @param transferTimeMinute
     * @return
     */
    private boolean isTransferTimeValid(RouteLabelDTO.RouteLabelRuleDTO rule, long transferTimeMinute) {
        if (Strings.isNullOrEmpty(rule.getTransTime())) {
            return true;
        }
        String[] timeArr = rule.getTransTime().split("-");
        long minTime = NumberUtil.parseLong(timeArr[0]);
        long maxTime = NumberUtil.parseLong(timeArr[1]);
        return transferTimeMinute >= minTime && transferTimeMinute <= maxTime;
    }

    /**
     * 承运航司是否在规则内
     * @param rule
     * @param operationAirLine1
     * @param operationAirLine2
     * @return
     */
    private boolean isCarrierValid(RouteLabelDTO.RouteLabelRuleDTO rule, String operationAirLine1, String operationAirLine2) {
        if (Strings.isNullOrEmpty(rule.getCarrier())) {
            return true;
        }
        List<String> carriers = Arrays.asList(rule.getCarrier().split("/"));
        return carriers.stream().anyMatch(carrier -> carrierMatches(carrier, operationAirLine1, operationAirLine2));
    }

    private boolean carrierMatches(String carrier, String operationAirLine1, String operationAirLine2) {
        if (carrier.contains("-")) {
            String[] carriers = carrier.split("-");
            return operationAirLine1.equals(carriers[0]) && operationAirLine2.equals(carriers[1]);
        } else {
            return carrier.contains(operationAirLine1) && carrier.contains(operationAirLine2);
        }
    }

    /**
     * 是否跨天是否在规则有效期内
     * @param rule
     * @param segOverNight
     * @return
     */
    private boolean isDateLimitValid(RouteLabelDTO.RouteLabelRuleDTO rule, boolean segOverNight) {
        if (Strings.isNullOrEmpty(rule.getTransDateLimit())) {
            return true;
        }
        if (rule.getTransDateLimit().contains("sameDay") && !segOverNight) {
            return true;
        }
        return rule.getTransDateLimit().contains("overnight") && segOverNight;
    }

}
