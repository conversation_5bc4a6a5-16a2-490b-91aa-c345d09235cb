package com.juneyaoair.oneorder.cuss.bean.result;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName TransferFlightInfo
 * @Description 中转住宿航班信息
 * <AUTHOR>
 * @Date 2025/9/01 14:01
 **/
@Data
public class TransferFlightInfo {

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "承运航司")
    private String operationAirLine;

    @ApiModelProperty(value = "航班日期 yyyy-MM-dd")
    private String flightDate;

    @ApiModelProperty(value = "出发机场三字码")
    private String depAirportCode;

    @ApiModelProperty(value = "出发机场名称")
    private String depAirportName;

    @ApiModelProperty(value = "出发城市三字码")
    private String depCity;

    @ApiModelProperty(value = "出发城市名称")
    private String depCityName;

    @ApiModelProperty(value = "起飞时间 格式：yyyy-MM-dd HH:mm")
    private String depTime;

    @ApiModelProperty(value = "到达机场三字码")
    private String arrAirportCode;

    @ApiModelProperty(value = "到达机场名称")
    private String arrAirportName;

    @ApiModelProperty(value = "到达城市三字码")
    private String arrCity;

    @ApiModelProperty(value = "到达城市名称")
    private String arrCityName;

    @ApiModelProperty(value = "到达时间 格式：yyyy-MM-dd HH:mm")
    private String arrTime;

}
